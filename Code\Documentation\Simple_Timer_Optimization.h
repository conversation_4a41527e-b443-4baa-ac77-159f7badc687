/*!
 * @file
 * @brief 简化的定时器系统位操作优化方案
 * 
 * 最小化修改，将List操作替换为位操作，保持原有API不变
 * 
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef SIMPLE_TIMER_OPTIMIZATION_H
#define SIMPLE_TIMER_OPTIMIZATION_H

#include <stdint.h>
#include <stdbool.h>

// ============================================================================
// 简化配置 - 保持与原系统一致
// ============================================================================

#define MAX_CALLBACK_TIMERS     20      // 与原配置一致
#define INVALID_TIMER_ID        0xFF    // 无效定时器ID

// ============================================================================
// 位操作宏定义
// ============================================================================

// 位操作辅助宏
#define SET_BIT(bitmap, bit)        ((bitmap) |= (1UL << (bit)))
#define CLEAR_BIT(bitmap, bit)      ((bitmap) &= ~(1UL << (bit)))
#define TEST_BIT(bitmap, bit)       (((bitmap) & (1UL << (bit))) != 0)
#define FIND_FIRST_SET(bitmap)      (__builtin_ctz(bitmap))
#define COUNT_BITS(bitmap)          (__builtin_popcount(bitmap))

// ============================================================================
// 简化的定时器结构 - 最小修改
// ============================================================================

// 保持原有定时器结构不变，只添加ID字段
typedef struct {
    st_CoreTimerLibTimer st_Timer;           // 原有基础定时器
    fpTimerCallBackFunction fp_CallBackFunction; // 原有回调函数
    uint16_t u16_DurationSeconds;            // 原有持续时间(秒)
    uint16_t u16_DurationMilliSeconds;       // 原有持续时间(毫秒)
    uint8_t u8_TimerType;                    // 原有定时器类型
    uint8_t u8_Priority;                     // 原有优先级
    uint8_t u8_TimerID;                      // 新增：定时器ID
    uint8_t u8_Reserved;                     // 对齐填充
} st_CoreCallbackTimer_Optimized;

// 简化的定时器池结构
typedef struct {
    st_CoreCallbackTimer_Optimized timers[MAX_CALLBACK_TIMERS];  // 定时器数组
    uint32_t active_bitmap;                  // 活跃定时器位图
    uint8_t next_free_id;                    // 下一个空闲ID
    uint8_t active_count;                    // 活跃定时器数量
} TimerPool_Simple;

// ============================================================================
// 简化API - 保持原有接口风格
// ============================================================================

// 初始化定时器池
void Core_CallbackTimer_InitOptimized(void);

// 创建定时器 - 返回定时器ID
uint8_t Core_CallbackTimer_Create(uint8_t u8_TimerType, uint8_t u8_Priority);

// 启动定时器 - 使用ID而非指针
bool Core_CallbackTimer_TimerStart(uint8_t u8_TimerID,
                                   fpTimerCallBackFunction fp_CallBackFunction,
                                   uint16_t u16_DurationSeconds,
                                   uint16_t u16_DurationMilliSeconds);

// 停止定时器 - 使用ID
bool Core_CallbackTimer_TimerStop(uint8_t u8_TimerID);

// 检查定时器是否运行 - 使用ID
bool Core_CallbackTimer_IsTimerRunning(uint8_t u8_TimerID);

// 处理定时器 - 保持原有接口
void Core_CallbackTimer_ProcessNormalPriorityTimers(void);

// ============================================================================
// 内联优化函数
// ============================================================================

// 快速查找空闲定时器ID
static inline uint8_t FindFreeTimerID(uint32_t active_bitmap) {
    uint32_t free_bitmap = ~active_bitmap;
    if (free_bitmap == 0) {
        return INVALID_TIMER_ID;
    }
    return FIND_FIRST_SET(free_bitmap);
}

// 快速检查定时器是否活跃
static inline bool IsTimerActive(uint32_t active_bitmap, uint8_t timer_id) {
    return TEST_BIT(active_bitmap, timer_id);
}

// 激活定时器
static inline void ActivateTimer(uint32_t *active_bitmap, uint8_t timer_id) {
    SET_BIT(*active_bitmap, timer_id);
}

// 停用定时器
static inline void DeactivateTimer(uint32_t *active_bitmap, uint8_t timer_id) {
    CLEAR_BIT(*active_bitmap, timer_id);
}

#endif // SIMPLE_TIMER_OPTIMIZATION_H
