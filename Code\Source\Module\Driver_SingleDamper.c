/*!
 * @file
 * @brief the single damper driver.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Driver_SingleDamper.h"
#include "ResolverDevice.h"
#include "Adpt_GPIO.h"
#include "FridgeRunner.h"
#include "SystemManager.h"
#include "stdio.h"

const uint16_t ARY_SingleDamper_RunSteps[DAMPER_MaxState][DAMPER_MaxState] = {
    { 0, 250, 550, 850, 1150, 1450, 1750 },
    { 350, 0, 250, 550, 850, 1150, 1450 },
    { 650, 350, 0, 250, 550, 850, 1150 },
    { 950, 650, 350, 0, 250, 550, 850 },
    { 1250, 950, 650, 350, 0, 250, 550 },
    { 1550, 1250, 950, 650, 350, 0, 250 },
    { 1850, 1550, 1250, 950, 650, 350, 0 }
};

SingleDamperDriver_st ARY_SingleDamperDriver[Damper_MaxNumber];

static void Config_RefDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB);
static void Config_MicroIceDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB);
static void Set_SingleDamperIOStatus(SingleDamperDriver_st *p_DamperInst);
static void Set_SingleDamperStepState(SingleDamperDriver_st *p_DamperInst);
static void Drive_SingleDamper(SingleDamperDriver_st *p_DamperInst);
static void Control_SingleDamper(SingleDamperDriver_st *p_DamperInst);

void Init_SingleDamper(void)
{
    // 初始化冷藏风门
    ARY_SingleDamperDriver[Damper_ID0].p_SetDamperIO = Config_RefDamperIO;
    ARY_SingleDamperDriver[Damper_ID0].damperNowState = DAMPER_AllClose;
    ARY_SingleDamperDriver[Damper_ID0].damperNewState = DAMPER_AllClose;
    ARY_SingleDamperDriver[Damper_ID0].b_IsDamperRunning = (bool)FALSE;
    ARY_SingleDamperDriver[Damper_ID0].id = Damper_ID0;
    ARY_SingleDamperDriver[Damper_ID0].b_IsDamperInitialized = (bool)TRUE;
    ARY_SingleDamperDriver[Damper_ID0].b_AlreadyRest = false;
    ARY_SingleDamperDriver[Damper_ID0].b_IsDamperFreeze = false;

    // 初始化微冰风门
    ARY_SingleDamperDriver[Damper_ID1].p_SetDamperIO = Config_MicroIceDamperIO;
    ARY_SingleDamperDriver[Damper_ID1].damperNowState = DAMPER_AllClose;
    ARY_SingleDamperDriver[Damper_ID1].damperNewState = DAMPER_AllClose;
    ARY_SingleDamperDriver[Damper_ID1].b_IsDamperRunning = (bool)FALSE;
    ARY_SingleDamperDriver[Damper_ID1].id = Damper_ID1;
    ARY_SingleDamperDriver[Damper_ID1].b_IsDamperInitialized = (bool)TRUE;
    ARY_SingleDamperDriver[Damper_ID1].b_AlreadyRest = false;
    ARY_SingleDamperDriver[Damper_ID1].b_IsDamperFreeze = false;
}

static void Config_RefDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB)
{
    if(true == b_IO_En)
    {
        IO_REF_DAMPER_ENABLE;
    }
    else
    {
        IO_REF_DAMPER_DISABLE;
    }

    if(true == b_IO_InA)
    {
        IO_REF_DAMPER_IN1_HIGH;
    }
    else
    {
        IO_REF_DAMPER_IN1_LOW;
    }

    if(true == b_IO_InB)
    {
        IO_REF_DAMPER_IN2_HIGH;
    }
    else
    {
        IO_REF_DAMPER_IN2_LOW;
    }
}

static void Config_MicroIceDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB)
{
    if(true == b_IO_En)
    {
        IO_MICROICE_DAMPER_ENABLE;
    }
    else
    {
        IO_MICROICE_DAMPER_DISABLE;
    }

    if(true == b_IO_InA)
    {
        IO_MICROICE_DAMPER_IN1_HIGH;
    }
    else
    {
        IO_MICROICE_DAMPER_IN1_LOW;
    }

    if(true == b_IO_InB)
    {
        IO_MICROICE_DAMPER_IN2_HIGH;
    }
    else
    {
        IO_MICROICE_DAMPER_IN2_LOW;
    }
}

static void Set_SingleDamperIOStatus(SingleDamperDriver_st *p_DamperInst)
{
    bool b_IO_En = false;
    bool b_IO_InA = false;
    bool b_IO_InB = false;

    if(false == p_DamperInst->b_IsDamperRunning)
    {
        p_DamperInst->damperStepState = DAMPER_STEP_STATE_A;
        b_IO_En = false;
        b_IO_InA = false;
        b_IO_InB = false;
    }
    else
    {
        b_IO_En = true;

        switch(p_DamperInst->damperStepState)
        {
            case DAMPER_STEP_STATE_A:
                b_IO_InA = false;
                b_IO_InB = false;
                break;

            case DAMPER_STEP_STATE_B:
                b_IO_InA = true;
                b_IO_InB = false;
                break;

            case DAMPER_STEP_STATE_C:
                b_IO_InA = true;
                b_IO_InB = true;
                break;

            case DAMPER_STEP_STATE_D:
                b_IO_InA = false;
                b_IO_InB = true;
                break;

            default:
                b_IO_InA = false;
                b_IO_InB = false;
                p_DamperInst->damperStepState = DAMPER_STEP_STATE_A;
                break;
        }
    }

    if(NULL != p_DamperInst->p_SetDamperIO)
    {
        p_DamperInst->p_SetDamperIO(b_IO_En, b_IO_InA, b_IO_InB);
    }
}

static void Set_SingleDamperStepState(SingleDamperDriver_st *p_DamperInst)
{
    if(p_DamperInst->damperStepState >= DAMPER_STEP_STATE_MAX)
    {
        p_DamperInst->damperStepState = DAMPER_STEP_STATE_D;
    }

    if(Con_SingleDamperOpenDirection == p_DamperInst->u8_DamperDirection) // OPEN
    {
        if(p_DamperInst->damperStepState == DAMPER_STEP_STATE_D)
        {
            p_DamperInst->damperStepState = DAMPER_STEP_STATE_A;
        }
        else
        {
            p_DamperInst->damperStepState++;
        }
    }
    else // CLOSE
    {
        if(p_DamperInst->damperStepState == DAMPER_STEP_STATE_A)
        {
            p_DamperInst->damperStepState = DAMPER_STEP_STATE_D;
        }
        else
        {
            p_DamperInst->damperStepState--;
        }
    }
}

static void Drive_SingleDamper(SingleDamperDriver_st *p_DamperInst)
{
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    if(0 == p_DamperInst->u8_DamperPPSTimer)
    {
        p_DamperInst->u8_DamperPPSTimer = Con_SingleDamperRunPPSTimeMs;

        if(0 == p_DamperInst->u16_DamperSteps)
        {
            if(true == p_DamperInst->b_IsDamperRunning)
            {
                p_DamperInst->u8_DamperIdleTimer = Con_SingleDamperStartDelayTimeSecond;
                p_DamperInst->u16_DamperNoActionSecond = 0;
                p_DamperInst->b_IsDamperRunning = false;
                if(p_DamperInst->damperNewState == p_DamperInst->damperNowState)
                {
                    p_DamperInst->b_NeedToResetDamper = false;
                }
            }

            if(b_energy_mode == true && p_DamperInst->id == Damper_MicroIce)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
            }
            else if(p_DamperInst->damperNowState == DAMPER_AllClose && p_DamperInst->id == Damper_MicroIce)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_On);
            }
            else if(p_DamperInst->id == Damper_MicroIce)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
            }
        }
        else
        {
            p_DamperInst->u16_DamperSteps--;
            p_DamperInst->b_IsDamperRunning = true;
            Set_SingleDamperStepState(p_DamperInst);
        }

        Set_SingleDamperIOStatus(p_DamperInst);
    }
    else
    {
        p_DamperInst->u8_DamperPPSTimer--;
    }
}

static void Control_SingleDamper(SingleDamperDriver_st *p_DamperInst)
{
    if((0 == p_DamperInst->u8_DamperIdleTimer) && (false == p_DamperInst->b_IsDamperRunning))
    {
        if(p_DamperInst->damperNewState != p_DamperInst->damperNowState)
        {
            if(DAMPER_AllClose == p_DamperInst->damperNowState)
            {
                p_DamperInst->damperMidState = p_DamperInst->damperNewState;
            }
            else
            {
                p_DamperInst->damperMidState = DAMPER_AllClose;
            }
        }
        else
        {
            if(true == p_DamperInst->b_NeedToResetDamper)
            {
                if(DAMPER_AllClose == p_DamperInst->damperNowState)
                {
                    p_DamperInst->damperMidState = DAMPER_AllOpen;
                }
                else
                {
                    p_DamperInst->damperMidState = DAMPER_AllClose;
                }
            }
        }

        if(p_DamperInst->damperMidState != p_DamperInst->damperNowState)
        {
            if(DAMPER_AllClose == p_DamperInst->damperMidState)
            {
                p_DamperInst->u8_DamperDirection = Con_SingleDamperCloseDirection;
            }
            else
            {
                p_DamperInst->u8_DamperDirection = Con_SingleDamperOpenDirection;
            }

            p_DamperInst->u16_DamperNoActionSecond = 0;
            p_DamperInst->u16_DamperSteps =
                ARY_SingleDamper_RunSteps[p_DamperInst->damperNowState][p_DamperInst->damperMidState];
            p_DamperInst->damperNowState = p_DamperInst->damperMidState;
            p_DamperInst->b_IsDamperRunning = true;
            if(p_DamperInst->id == Damper_MicroIce)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_VarDamperHeater, DS_Off);
            }
        }
    }
}

void Execute_SingleDamperDriver(void)
{
    uint8_t u8_Index = 0;
    SingleDamperDriver_st *p_ARY_DamperDriver;
    p_ARY_DamperDriver = ARY_SingleDamperDriver;

    for(u8_Index = 0; u8_Index < (uint8_t)(Damper_MaxNumber); u8_Index++)
    {
        p_ARY_DamperDriver = &ARY_SingleDamperDriver[u8_Index];
        if(p_ARY_DamperDriver->b_IsDamperInitialized == true &&
            p_ARY_DamperDriver->b_IsDamperFreeze == false)
        {
            Control_SingleDamper(p_ARY_DamperDriver);
            Drive_SingleDamper(p_ARY_DamperDriver);
        }
    }
}

void Set_SingleDamperState(SingleDamperDriver_st *p_DamperInst, DamperState_t settingState)
{
    if(DAMPER_FREEZED == settingState)
    {
        p_DamperInst->b_IsDamperFreeze = true;
    }
    else
    {
        p_DamperInst->b_IsDamperFreeze = false;
        if(settingState >= DAMPER_MaxState)
        {
            settingState = DAMPER_AllClose;
        }
        else if(false == p_DamperInst->b_NeedToResetDamper)
        {
            p_DamperInst->damperNewState = settingState;
        }
    }
}

void Set_RefDamperState(DamperState_t settingState)
{
    if(Damper_Ref != Damper_Invaild)
    {
        Set_SingleDamperState(&ARY_SingleDamperDriver[Damper_Ref], settingState);
    }
}

void Set_VarDamperState(DamperState_t settingState)
{
    if(Damper_MicroIce != Damper_Invaild)
    {
        Set_SingleDamperState(&ARY_SingleDamperDriver[Damper_MicroIce], settingState);
    }
}

bool Get_RefDamperState(void)
{
    if(Damper_Ref != Damper_Invaild)
    {
        return (ARY_SingleDamperDriver[Damper_Ref].damperNowState != DAMPER_AllClose);
    }
    else
    {
        return true;
    }
}

bool Get_VarDamperState(void)
{
    // 兼容性接口，变温风门现在指向微冰风门
    if(Damper_MicroIce != Damper_Invaild)
    {
        return (ARY_SingleDamperDriver[Damper_MicroIce].damperNowState != DAMPER_AllClose);
    }
    else
    {
        return true;
    }
}

bool IsSingleDamperAlreadyReset(DamperID_t damperID)
{
    if(damperID >= DamperIDMax)
    {
        damperID = Damper_ID0;
    }
    return ARY_SingleDamperDriver[damperID].b_AlreadyRest;
}

void Reset_SingleDamper(DamperID_t damperID)
{
    if(damperID >= DamperIDMax)
    {
        damperID = Damper_ID0;
    }

    ARY_SingleDamperDriver[damperID].b_NeedToResetDamper = true;
    if(ARY_SingleDamperDriver[damperID].b_AlreadyRest == false)
    {
        ARY_SingleDamperDriver[damperID].b_AlreadyRest = true;
    }
}

void Freeze_SingleDamper(DamperID_t damperID, bool enable)
{
    if(damperID >= DamperIDMax)
    {
        damperID = Damper_ID0;
    }
    ARY_SingleDamperDriver[damperID].b_IsDamperFreeze = enable;
}

void ForcedCtrl_SingleDamper(DamperID_t damperID, bool b_IsDamperForcedRunning)
{
    if(damperID >= DamperIDMax)
    {
        damperID = Damper_ID0;
    }

    if(false == b_IsDamperForcedRunning)
    {
        ARY_SingleDamperDriver[damperID].b_IsDamperRunning = false;
        ARY_SingleDamperDriver[damperID].u16_DamperSteps = 0;
    }
    else
    {
        ARY_SingleDamperDriver[damperID].damperNowState = DAMPER_AllClose;
        ARY_SingleDamperDriver[damperID].damperNewState = DAMPER_AllClose;
        ARY_SingleDamperDriver[damperID].u16_DamperSteps = Con_SingleDamperCheckPartSteps;
        ARY_SingleDamperDriver[damperID].u8_DamperDirection = Con_SingleDamperOpenDirection;
        ARY_SingleDamperDriver[damperID].b_IsDamperRunning = true;
    }
}

void Handle_SingleDamperTimer(void)
{
    uint8_t u8_Index = 0;
    SingleDamperDriver_st *p_ARY_DamperDriver;
    p_ARY_DamperDriver = ARY_SingleDamperDriver;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    FridgeState_t fridge_state = Get_FridgeState();

    for(u8_Index = 0; u8_Index < (uint8_t)(Damper_MaxNumber); u8_Index++)
    {
        if(p_ARY_DamperDriver->u8_DamperIdleTimer > 0)
        {
            p_ARY_DamperDriver->u8_DamperIdleTimer--;
        }

        if(p_ARY_DamperDriver->u16_DamperNoActionSecond < 0xFFFF)
        {
            if((false == b_energy_mode) && (eFridge_Showroom != fridge_state))
            {
                p_ARY_DamperDriver->u16_DamperNoActionSecond++;
            }

            if(Con_SingleDamperTempResetTimeSecond <= p_ARY_DamperDriver->u16_DamperNoActionSecond)
            {
                Reset_SingleDamper(u8_Index);
            }
        }

        p_ARY_DamperDriver++;
    }
}

void RefDamper_Init(void)
{
    Reset_SingleDamper(Damper_ID0);
}
