/*!
 * @file
 * @brief Enables timers to be used with callback functions and provides a way for
 * timers to be used without having to poll them.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __Core_CallbackTimer_H__
#define __Core_CallbackTimer_H__

#include <stdbool.h>
#include "Core_Types.h"
#include "Core_TimerLibrary.h"
#include "CoreUser_CallBackTimer_Config.h"

#ifndef NUM_ELEMENTS

#define NUM_ELEMENTS(array) (sizeof(array) / sizeof(array[0]))
#define ELEMENT_COUNT NUM_ELEMENTS
#define ELEMENT_SIZE(array) (sizeof(array[0]))

#endif

// 位操作宏定义 - 用于优化定时器管理
#define MYSET_BIT(bitmap, bit) ((bitmap) |= (1UL << (bit)))
#define MYCLEAR_BIT(bitmap, bit) ((bitmap) &= ~(1UL << (bit)))
#define MYTEST_BIT(bitmap, bit) (((bitmap) & (1UL << (bit))) != 0)
#define FIND_FIRST_SET(bitmap) (__builtin_ctz(bitmap))
#define COUNT_BITS(bitmap) (__builtin_popcount(bitmap))

#define INVALID_TIMER_ID ((uint8_t)0xFF)

#if (true == CALLBACK_TIMER_CALLBACK_DATA_ENABLED)
typedef void (*fpTimerCallBackFunction)(void *);
#else
typedef void (*fpTimerCallBackFunction)(void);
#endif

typedef enum
{
    eCallbackTimer_Type_OneShot = 0,
    eCallbackTimer_Type_Periodic,
    eCallbackTimer_Type_Max
} EN_Core_CallbackTimer_Type;

typedef enum
{
    eCallbackTimer_Priority_Normal = 0,
    eCallbackTimer_Priority_High,
    eCallbackTimer_Priority_Max
} EN_Core_CallbackTimer_Priority;

typedef struct
{
    st_CoreTimerLibTimer st_Timer;
    fpTimerCallBackFunction fp_CallBackFunction;
    uint16_t u16_DurationSeconds;
    uint16_t u16_DurationMilliSeconds;
    uint8_t u8_TimerType;
    uint8_t u8_Priority;
    uint8_t u8_TimerID; // 新增：定时器ID
    uint8_t u8_Reserved; // 对齐填充
} st_CoreCallbackTimer;

/*!
 * @brief Initializes the timer callback pool(s).
 */
extern void Core_CallbackTimer_Init(void);

/*!
 * @brief Services the timers in the pool.
 */
extern void Core_CallbackTimer_Update(void);

/*!
 * @brief Used to add a timer and its corresponding callback function to the pool of
 * timers that it being serviced by the TimerCallBack module.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 * @param fpTimerCallBackFunction fp_CallBackFunction : Function called upon expiration of the timer
 * @param void * p_CallbackData : Data sent to callback function on expiration of the timer
 * @param uint16_t u16_DurationSeconds, uint16_t u16_DurationMilliSeconds : Time until expiration of the timer
 * @param EN_Core_CallbackTimer_Type en_TimerType : Type (one-shot or periodic) of the timer One-shot
 * timers are removed from the pool after expiration. Periodic timers are reloaded on expiration.
 * @param EN_Core_CallbackTimer_Priority en_Priority : Priority (high or normal) of the timer
 * @retval true if all init parameters are valid, false otherwise.
 */
extern void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

/*!
 * @brief Removes a timer and its corresponding callback functions from the pool
 * of timers serviced by the TimerCallback module.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 */
extern void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer);

/*!
 * @brief Checks whether a callback timer currently resides in the timer pool.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 * @retval true if timer is running, false otherwise
 */
extern bool Core_CallbackTimer_B_IsTimerRunning(
    st_CoreCallbackTimer *pst_CallBackTimer);

/*!
 * @brief Get the time remaining if a timer is running.  If timer not running,
 * does not modify the seconds or milliseconds arguments.
 * @param st_CoreCallbackTimer * pst_CallBackTimer : The timer
 * @param uint16_t * pu16_Seconds, uint16_t *pu16_Milliseconds : Pointers to where the
 * seconds and milliseconds will be
 */
extern void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds);

/*!
 * @brief 创建定时器 - 优化版本，返回定时器ID
 * @param en_TimerType 定时器类型
 * @param en_Priority 优先级
 * @return 定时器ID，失败返回INVALID_TIMER_ID
 */
extern uint8_t Core_CallbackTimer_Create(
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

/*!
 * @brief 启动定时器 - 优化版本，使用定时器ID
 * @param u8_TimerID 定时器ID
 * @param fp_CallBackFunction 回调函数
 * @param u16_DurationSeconds 持续时间(秒)
 * @param u16_DurationMilliSeconds 持续时间(毫秒)
 * @return 成功返回true
 */
extern bool Core_CallbackTimer_StartByID(
    uint8_t u8_TimerID,
    fpTimerCallBackFunction fp_CallBackFunction,
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds);

/*!
 * @brief 停止定时器 - 优化版本，使用定时器ID
 * @param u8_TimerID 定时器ID
 * @return 成功返回true
 */
extern bool Core_CallbackTimer_StopByID(uint8_t u8_TimerID);

/*!
 * @brief 检查定时器是否运行 - 优化版本，使用定时器ID
 * @param u8_TimerID 定时器ID
 * @return 运行中返回true
 */
extern bool Core_CallbackTimer_IsRunningByID(uint8_t u8_TimerID);

/*!
 * @brief 获取活跃定时器数量
 * @return 活跃定时器数量
 */
extern uint8_t Core_CallbackTimer_GetActiveCount(void);

#endif
