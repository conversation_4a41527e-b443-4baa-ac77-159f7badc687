/*!
 * @file
 * @brief 简化的定时器系统位操作优化实现
 * 
 * 最小化修改，将List操作替换为位操作
 * 
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Simple_Timer_Optimization.h"
#include "Core_TimerLibrary.h"
#include "Core_TimeBase.h"

// ============================================================================
// 全局变量 - 替换原有的List结构
// ============================================================================

// 简化的定时器池 - 替换原有的指针数组和List
static TimerPool_Simple g_timer_pool = {0};

// ============================================================================
// 核心实现 - 最小化修改
// ============================================================================

/*!
 * @brief 初始化定时器池
 */
void Core_CallbackTimer_InitOptimized(void) {
    // 清零所有数据
    memset(&g_timer_pool, 0, sizeof(g_timer_pool));
    
    // 初始化所有定时器ID
    for (uint8_t i = 0; i < MAX_CALLBACK_TIMERS; i++) {
        g_timer_pool.timers[i].u8_TimerID = i;
    }
}

/*!
 * @brief 创建定时器 - O(1)操作
 * @param u8_TimerType 定时器类型
 * @param u8_Priority 优先级
 * @return 定时器ID，失败返回INVALID_TIMER_ID
 */
uint8_t Core_CallbackTimer_Create(uint8_t u8_TimerType, uint8_t u8_Priority) {
    // 快速查找空闲ID - O(1)操作
    uint8_t timer_id = FindFreeTimerID(g_timer_pool.active_bitmap);
    
    if (timer_id >= MAX_CALLBACK_TIMERS) {
        return INVALID_TIMER_ID; // 池满
    }
    
    // 初始化定时器
    st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[timer_id];
    timer->u8_TimerType = u8_TimerType;
    timer->u8_Priority = u8_Priority;
    timer->u8_TimerID = timer_id;
    timer->fp_CallBackFunction = NULL;
    
    return timer_id;
}

/*!
 * @brief 启动定时器 - O(1)操作，替换原有的List插入
 * @param u8_TimerID 定时器ID
 * @param fp_CallBackFunction 回调函数
 * @param u16_DurationSeconds 持续时间(秒)
 * @param u16_DurationMilliSeconds 持续时间(毫秒)
 * @return 成功返回true
 */
bool Core_CallbackTimer_TimerStart(uint8_t u8_TimerID,
                                   fpTimerCallBackFunction fp_CallBackFunction,
                                   uint16_t u16_DurationSeconds,
                                   uint16_t u16_DurationMilliSeconds) {
    // 参数检查
    if (u8_TimerID >= MAX_CALLBACK_TIMERS || fp_CallBackFunction == NULL) {
        return false;
    }
    
    st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[u8_TimerID];
    
    // 设置定时器参数
    timer->fp_CallBackFunction = fp_CallBackFunction;
    timer->u16_DurationSeconds = u16_DurationSeconds;
    timer->u16_DurationMilliSeconds = u16_DurationMilliSeconds;
    
    // 启动基础定时器
    Core_TimerLib_TimerStart(&timer->st_Timer, u16_DurationSeconds, u16_DurationMilliSeconds);
    
    // 激活定时器 - O(1)位操作，替换List插入
    ActivateTimer(&g_timer_pool.active_bitmap, u8_TimerID);
    g_timer_pool.active_count++;
    
    return true;
}

/*!
 * @brief 停止定时器 - O(1)操作，替换原有的List移除
 * @param u8_TimerID 定时器ID
 * @return 成功返回true
 */
bool Core_CallbackTimer_TimerStop(uint8_t u8_TimerID) {
    // 参数检查
    if (u8_TimerID >= MAX_CALLBACK_TIMERS) {
        return false;
    }
    
    // 检查定时器是否活跃
    if (!IsTimerActive(g_timer_pool.active_bitmap, u8_TimerID)) {
        return false; // 定时器未运行
    }
    
    st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[u8_TimerID];
    
    // 停止基础定时器
    Core_TimerLib_TimerStop(&timer->st_Timer);
    
    // 停用定时器 - O(1)位操作，替换List移除
    DeactivateTimer(&g_timer_pool.active_bitmap, u8_TimerID);
    g_timer_pool.active_count--;
    
    // 清除回调函数
    timer->fp_CallBackFunction = NULL;
    
    return true;
}

/*!
 * @brief 检查定时器是否运行 - O(1)操作，替换原有的List查找
 * @param u8_TimerID 定时器ID
 * @return 运行中返回true
 */
bool Core_CallbackTimer_IsTimerRunning(uint8_t u8_TimerID) {
    if (u8_TimerID >= MAX_CALLBACK_TIMERS) {
        return false;
    }
    
    // O(1)位操作，替换原有的List_B_Contains
    return IsTimerActive(g_timer_pool.active_bitmap, u8_TimerID);
}

/*!
 * @brief 处理单个定时器 - 保持原有逻辑
 * @param timer 定时器指针
 * @return 是否处理了定时器
 */
static bool ProcessSingleTimer(st_CoreCallbackTimer_Optimized *timer) {
    // 检查定时器是否到期 - 保持原有逻辑
    if (!Core_TimerLib_IsTimerExpired(&timer->st_Timer)) {
        return false;
    }
    
    // 缓存回调函数 - 保持原有逻辑
    fpTimerCallBackFunction fp_CallBackFunction = timer->fp_CallBackFunction;
    
    // 处理不同类型的定时器 - 保持原有逻辑
    if (eCallbackTimer_Type_Periodic == timer->u8_TimerType) {
        // 周期性定时器：重新启动
        Core_TimerLib_TimerStart(&timer->st_Timer,
                                timer->u16_DurationSeconds,
                                timer->u16_DurationMilliSeconds);
    } else {
        // 一次性定时器：停止
        Core_CallbackTimer_TimerStop(timer->u8_TimerID);
    }
    
    // 执行回调 - 保持原有逻辑
    if (fp_CallBackFunction != NULL) {
        fp_CallBackFunction();
    }
    
    return true;
}

/*!
 * @brief 处理普通优先级定时器 - 优化的轮询算法
 * 替换原有的线性遍历为位操作遍历
 */
void Core_CallbackTimer_ProcessNormalPriorityTimers(void) {
    // 快速检查：如果没有活跃定时器，直接返回
    if (g_timer_pool.active_count == 0) {
        return;
    }
    
    static uint8_t last_processed_id = 0; // 保持轮询公平性
    uint32_t active_bitmap = g_timer_pool.active_bitmap;
    uint8_t processed_count = 0;
    
    // 从上次处理的位置开始，实现轮询
    uint8_t start_id = last_processed_id;
    uint8_t current_id = start_id;
    
    do {
        // 检查当前ID是否活跃
        if (TEST_BIT(active_bitmap, current_id)) {
            st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[current_id];
            
            if (ProcessSingleTimer(timer)) {
                last_processed_id = (current_id + 1) % MAX_CALLBACK_TIMERS;
                return; // 处理了一个定时器就返回，保持原有行为
            }
        }
        
        // 移动到下一个定时器
        current_id = (current_id + 1) % MAX_CALLBACK_TIMERS;
        processed_count++;
        
    } while (current_id != start_id && processed_count < MAX_CALLBACK_TIMERS);
    
    // 更新下次开始位置
    last_processed_id = current_id;
}

/*!
 * @brief 批量处理所有到期定时器 - 新增高效版本
 * 可选择使用此函数替代轮询版本以获得更好性能
 */
void Core_CallbackTimer_ProcessAllExpired(void) {
    uint32_t active_bitmap = g_timer_pool.active_bitmap;
    
    // 使用位操作快速遍历所有活跃定时器
    while (active_bitmap != 0) {
        uint8_t timer_id = FIND_FIRST_SET(active_bitmap);
        active_bitmap &= (active_bitmap - 1); // 清除最低位的1
        
        st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[timer_id];
        ProcessSingleTimer(timer);
    }
}

/*!
 * @brief 获取活跃定时器数量 - 新增调试函数
 */
uint8_t Core_CallbackTimer_GetActiveCount(void) {
    return g_timer_pool.active_count;
}

/*!
 * @brief 打印定时器状态 - 新增调试函数
 */
void Core_CallbackTimer_PrintStatus(void) {
    printf("Timer Pool Status:\n");
    printf("Active Count: %d/%d\n", g_timer_pool.active_count, MAX_CALLBACK_TIMERS);
    printf("Active Bitmap: 0x%08X\n", g_timer_pool.active_bitmap);
    
    for (uint8_t i = 0; i < MAX_CALLBACK_TIMERS; i++) {
        if (TEST_BIT(g_timer_pool.active_bitmap, i)) {
            st_CoreCallbackTimer_Optimized *timer = &g_timer_pool.timers[i];
            printf("Timer[%d]: Type=%d, Priority=%d\n", 
                   i, timer->u8_TimerType, timer->u8_Priority);
        }
    }
}

// ============================================================================
// 兼容性包装函数 - 可选
// ============================================================================

/*!
 * @brief 兼容性函数：模拟原有的指针方式创建定时器
 * 可以逐步迁移现有代码
 */
st_CoreCallbackTimer_Optimized* Core_CallbackTimer_CreateLegacy(uint8_t u8_TimerType, uint8_t u8_Priority) {
    uint8_t timer_id = Core_CallbackTimer_Create(u8_TimerType, u8_Priority);
    
    if (timer_id == INVALID_TIMER_ID) {
        return NULL;
    }
    
    return &g_timer_pool.timers[timer_id];
}

/*!
 * @brief 兼容性函数：从定时器指针获取ID
 */
uint8_t Core_CallbackTimer_GetIDFromPointer(st_CoreCallbackTimer_Optimized *timer) {
    if (timer == NULL) {
        return INVALID_TIMER_ID;
    }
    
    // 计算数组索引
    ptrdiff_t index = timer - g_timer_pool.timers;
    
    if (index >= 0 && index < MAX_CALLBACK_TIMERS) {
        return (uint8_t)index;
    }
    
    return INVALID_TIMER_ID;
}
