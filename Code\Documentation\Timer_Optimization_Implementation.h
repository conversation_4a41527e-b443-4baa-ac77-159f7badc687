/*!
 * @file
 * @brief 优化后的定时器系统实现头文件
 * 
 * 本文件展示了基于分析报告的优化实现方案
 * 
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef TIMER_OPTIMIZATION_IMPLEMENTATION_H
#define TIMER_OPTIMIZATION_IMPLEMENTATION_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

// ============================================================================
// 配置定义
// ============================================================================

#define MAX_TIMERS                          32      // 最大定时器数量（2的幂次，便于位操作）
#define MAX_TIMER_GROUPS                    8       // 最大定时器组数量
#define MAX_CALLBACK_TIME_US                1000    // 最大回调执行时间（微秒）
#define TIMER_OVERFLOW_CHECK_THRESHOLD      (UINT32_MAX / 2)

// 调试和统计功能开关
#define TIMER_DEBUG_ENABLED                 1
#define TIMER_STATISTICS_ENABLED            1
#define TIMER_PERFORMANCE_MONITORING        1

// ============================================================================
// 数据类型定义
// ============================================================================

// 定时器类型枚举
typedef enum {
    TIMER_TYPE_ONESHOT = 0,         // 一次性定时器
    TIMER_TYPE_PERIODIC,            // 周期性定时器
    TIMER_TYPE_COUNTDOWN,           // 倒计时定时器
    TIMER_TYPE_STOPWATCH,           // 秒表定时器
    TIMER_TYPE_RATE_LIMITED,        // 限速定时器
    TIMER_TYPE_DEBOUNCE,            // 防抖定时器
    TIMER_TYPE_MAX
} TimerType_t;

// 定时器状态枚举
typedef enum {
    TIMER_STATE_STOPPED = 0,        // 停止状态
    TIMER_STATE_RUNNING,            // 运行状态
    TIMER_STATE_PAUSED,             // 暂停状态
    TIMER_STATE_EXPIRED,            // 已到期状态
    TIMER_STATE_ERROR,              // 错误状态
    TIMER_STATE_MAX
} TimerState_t;

// 定时器优先级枚举
typedef enum {
    TIMER_PRIORITY_LOW = 0,         // 低优先级
    TIMER_PRIORITY_NORMAL,          // 普通优先级
    TIMER_PRIORITY_HIGH,            // 高优先级
    TIMER_PRIORITY_CRITICAL,        // 关键优先级
    TIMER_PRIORITY_MAX
} TimerPriority_t;

// 定时器错误类型
typedef enum {
    TIMER_ERROR_NONE = 0,
    TIMER_ERROR_INVALID_PARAM,
    TIMER_ERROR_POOL_FULL,
    TIMER_ERROR_NOT_FOUND,
    TIMER_ERROR_CALLBACK_TIMEOUT,
    TIMER_ERROR_CALLBACK_EXCEPTION,
    TIMER_ERROR_OVERFLOW,
    TIMER_ERROR_MAX
} TimerError_t;

// 回调函数类型定义
typedef void (*TimerCallback_t)(void *context);
typedef void (*TimerErrorHandler_t)(uint8_t timer_id, TimerError_t error);

// 优化后的定时器结构体（内存对齐优化）
typedef struct __attribute__((packed, aligned(16))) {
    // 核心定时器数据（16字节对齐）
    uint32_t next_expire_tick;      // 预计算的到期时间
    uint32_t duration_ticks;        // 持续时间（tick）
    TimerCallback_t callback;       // 回调函数指针
    void *context;                  // 回调上下文数据
    
    // 控制和状态信息（8字节）
    uint16_t duration_seconds;      // 持续时间（秒）
    uint16_t duration_milliseconds; // 持续时间（毫秒）
    uint8_t timer_type;             // 定时器类型
    uint8_t priority;               // 优先级
    uint8_t state;                  // 当前状态
    uint8_t group_id;               // 所属组ID
    
    // 调试和统计信息（条件编译）
#if TIMER_DEBUG_ENABLED
    const char *name;               // 定时器名称
    const char *file;               // 创建文件
    uint16_t line;                  // 创建行号
    uint16_t reserved;              // 对齐填充
#endif
} OptimizedTimer_t;

// 定时器统计信息
#if TIMER_STATISTICS_ENABLED
typedef struct {
    uint32_t creation_time;         // 创建时间
    uint32_t last_expire_time;      // 最后到期时间
    uint32_t total_expires;         // 总到期次数
    uint32_t total_execution_time_us; // 总执行时间
    uint32_t max_execution_time_us; // 最大执行时间
    uint32_t min_execution_time_us; // 最小执行时间
} TimerStatistics_t;
#endif

// 定时器组结构体
typedef struct {
    uint32_t timer_bitmap;          // 组内定时器位图
    uint8_t group_id;               // 组ID
    uint8_t priority;               // 组优先级
    bool enabled;                   // 组启用状态
    uint8_t reserved;               // 对齐填充
} TimerGroup_t;

// 定时器池结构体（使用位图优化）
typedef struct {
    OptimizedTimer_t timers[MAX_TIMERS];    // 定时器数组
    uint32_t active_bitmap;                 // 活跃定时器位图
    uint32_t expired_bitmap;                // 已到期定时器位图
    uint8_t next_free_index;                // 下一个空闲索引
    uint8_t active_count;                   // 活跃定时器数量
    uint16_t reserved;                      // 对齐填充
    
#if TIMER_STATISTICS_ENABLED
    TimerStatistics_t statistics[MAX_TIMERS]; // 统计信息数组
#endif
} TimerPool_t;

// 性能监控结构体
#if TIMER_PERFORMANCE_MONITORING
typedef struct {
    uint32_t process_start_time;    // 处理开始时间
    uint32_t process_end_time;      // 处理结束时间
    uint32_t timers_processed;      // 已处理定时器数量
    uint32_t callbacks_executed;    // 已执行回调数量
    uint32_t max_process_time_us;   // 最大处理时间
    uint32_t avg_process_time_us;   // 平均处理时间
    uint32_t total_process_calls;   // 总处理调用次数
} PerformanceStats_t;
#endif

// 系统配置结构体
typedef struct {
    TimerErrorHandler_t error_handler;      // 错误处理函数
    uint32_t max_callback_time_us;          // 最大回调时间
    bool enable_overflow_protection;        // 启用溢出保护
    bool enable_callback_timeout;           // 启用回调超时检查
    bool enable_statistics;                 // 启用统计功能
    bool enable_performance_monitoring;     // 启用性能监控
} TimerSystemConfig_t;

// ============================================================================
// 核心API函数声明
// ============================================================================

// 系统初始化和配置
TimerError_t TimerSystem_Init(const TimerSystemConfig_t *config);
TimerError_t TimerSystem_Deinit(void);
TimerError_t TimerSystem_Configure(const TimerSystemConfig_t *config);

// 定时器生命周期管理
uint8_t TimerSystem_Create(const char *name, TimerType_t type, TimerPriority_t priority);
TimerError_t TimerSystem_Destroy(uint8_t timer_id);
TimerError_t TimerSystem_Start(uint8_t timer_id, TimerCallback_t callback, void *context,
                              uint16_t seconds, uint16_t milliseconds);
TimerError_t TimerSystem_Stop(uint8_t timer_id);
TimerError_t TimerSystem_Pause(uint8_t timer_id);
TimerError_t TimerSystem_Resume(uint8_t timer_id);
TimerError_t TimerSystem_Reset(uint8_t timer_id);

// 定时器状态查询
TimerState_t TimerSystem_GetState(uint8_t timer_id);
bool TimerSystem_IsRunning(uint8_t timer_id);
bool TimerSystem_IsExpired(uint8_t timer_id);
uint32_t TimerSystem_GetRemainingTime(uint8_t timer_id);
uint32_t TimerSystem_GetElapsedTime(uint8_t timer_id);

// 定时器组管理
uint8_t TimerSystem_CreateGroup(TimerPriority_t priority);
TimerError_t TimerSystem_AddToGroup(uint8_t timer_id, uint8_t group_id);
TimerError_t TimerSystem_RemoveFromGroup(uint8_t timer_id, uint8_t group_id);
TimerError_t TimerSystem_StartGroup(uint8_t group_id);
TimerError_t TimerSystem_StopGroup(uint8_t group_id);
TimerError_t TimerSystem_EnableGroup(uint8_t group_id, bool enable);

// 高级定时器功能
TimerError_t TimerSystem_SetDebounce(uint8_t timer_id, uint16_t debounce_ms);
TimerError_t TimerSystem_SetRateLimit(uint8_t timer_id, uint32_t max_rate_hz);
TimerError_t TimerSystem_StartStopwatch(uint8_t timer_id);
uint32_t TimerSystem_GetStopwatchTime(uint8_t timer_id);

// 系统处理和维护
void TimerSystem_Process(void);
void TimerSystem_ProcessHighPriority(void);
TimerError_t TimerSystem_Cleanup(void);

// 调试和诊断
#if TIMER_DEBUG_ENABLED
void TimerSystem_PrintStatus(void);
void TimerSystem_PrintTimer(uint8_t timer_id);
void TimerSystem_PrintGroup(uint8_t group_id);
const char* TimerSystem_GetErrorString(TimerError_t error);
#endif

// 统计和性能监控
#if TIMER_STATISTICS_ENABLED
const TimerStatistics_t* TimerSystem_GetStatistics(uint8_t timer_id);
void TimerSystem_ResetStatistics(uint8_t timer_id);
void TimerSystem_PrintStatistics(void);
#endif

#if TIMER_PERFORMANCE_MONITORING
const PerformanceStats_t* TimerSystem_GetPerformanceStats(void);
void TimerSystem_ResetPerformanceStats(void);
#endif

// ============================================================================
// 内联优化函数
// ============================================================================

// 快速位操作辅助函数
static inline uint8_t FindFirstSetBit(uint32_t bitmap) {
    return __builtin_ctz(bitmap);  // GCC内建函数，高效
}

static inline uint8_t CountSetBits(uint32_t bitmap) {
    return __builtin_popcount(bitmap);
}

static inline void SetBit(uint32_t *bitmap, uint8_t bit) {
    *bitmap |= (1U << bit);
}

static inline void ClearBit(uint32_t *bitmap, uint8_t bit) {
    *bitmap &= ~(1U << bit);
}

static inline bool TestBit(uint32_t bitmap, uint8_t bit) {
    return (bitmap & (1U << bit)) != 0;
}

// 时间转换辅助函数
static inline uint32_t SecondsToTicks(uint16_t seconds) {
    return seconds * CORE_TIMEBASE_NUM_TICKS_PER_SECOND;
}

static inline uint32_t MillisecondsToTicks(uint16_t milliseconds) {
    return milliseconds * COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
}

static inline uint32_t TimeToTicks(uint16_t seconds, uint16_t milliseconds) {
    return SecondsToTicks(seconds) + MillisecondsToTicks(milliseconds);
}

// 溢出安全的时间比较
static inline bool IsTimeExpired(uint32_t expire_time, uint32_t current_time) {
    // 处理32位溢出的安全比较
    if (current_time < expire_time) {
        // 检查是否是溢出情况
        return (expire_time - current_time) > TIMER_OVERFLOW_CHECK_THRESHOLD;
    }
    return current_time >= expire_time;
}

// ============================================================================
// 宏定义辅助
// ============================================================================

// 调试模式下的定时器创建宏
#if TIMER_DEBUG_ENABLED
#define TIMER_CREATE(name, type, priority) \
    TimerSystem_CreateDebug(name, type, priority, __FILE__, __LINE__)

#define TIMER_START(id, callback, context, sec, ms) \
    TimerSystem_StartDebug(id, callback, context, sec, ms, __FILE__, __LINE__)
#else
#define TIMER_CREATE(name, type, priority) \
    TimerSystem_Create(name, type, priority)

#define TIMER_START(id, callback, context, sec, ms) \
    TimerSystem_Start(id, callback, context, sec, ms)
#endif

// 错误检查宏
#define TIMER_CHECK_ID(id) \
    do { \
        if ((id) >= MAX_TIMERS) return TIMER_ERROR_INVALID_PARAM; \
    } while(0)

#define TIMER_CHECK_POINTER(ptr) \
    do { \
        if ((ptr) == NULL) return TIMER_ERROR_INVALID_PARAM; \
    } while(0)

// 性能测量宏
#if TIMER_PERFORMANCE_MONITORING
#define TIMER_PERF_START() \
    uint32_t _perf_start = GetMicrosecondTick()

#define TIMER_PERF_END(operation) \
    do { \
        uint32_t _perf_end = GetMicrosecondTick(); \
        UpdatePerformanceCounter(operation, _perf_end - _perf_start); \
    } while(0)
#else
#define TIMER_PERF_START()
#define TIMER_PERF_END(operation)
#endif

#endif // TIMER_OPTIMIZATION_IMPLEMENTATION_H
