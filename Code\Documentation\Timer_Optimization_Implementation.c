/*!
 * @file
 * @brief 优化后的定时器系统核心实现
 * 
 * 本文件展示了基于分析报告的优化实现方案的核心代码
 * 
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Timer_Optimization_Implementation.h"
#include "Core_TimeBase.h"
#include <string.h>
#include <setjmp.h>

// ============================================================================
// 全局变量和静态数据
// ============================================================================

// 定时器池（主要数据结构）
static TimerPool_t g_timer_pool = {0};

// 定时器组数组
static TimerGroup_t g_timer_groups[MAX_TIMER_GROUPS] = {0};

// 系统配置
static TimerSystemConfig_t g_system_config = {0};

// 性能统计
#if TIMER_PERFORMANCE_MONITORING
static PerformanceStats_t g_performance_stats = {0};
#endif

// 异常处理跳转点
static jmp_buf g_callback_exception_handler;

// ============================================================================
// 内部辅助函数
// ============================================================================

/**
 * @brief 查找下一个空闲的定时器索引
 * @return 空闲索引，如果池满则返回MAX_TIMERS
 */
static uint8_t FindNextFreeIndex(void) {
    uint32_t free_bitmap = ~g_timer_pool.active_bitmap;
    
    if (free_bitmap == 0) {
        return MAX_TIMERS; // 池满
    }
    
    return FindFirstSetBit(free_bitmap);
}

/**
 * @brief 更新定时器统计信息
 * @param timer_id 定时器ID
 * @param execution_time_us 执行时间（微秒）
 */
#if TIMER_STATISTICS_ENABLED
static void UpdateTimerStatistics(uint8_t timer_id, uint32_t execution_time_us) {
    TimerStatistics_t *stats = &g_timer_pool.statistics[timer_id];
    
    stats->total_expires++;
    stats->total_execution_time_us += execution_time_us;
    stats->last_expire_time = Core_TimeBase_GetSystemTickCounter();
    
    if (execution_time_us > stats->max_execution_time_us) {
        stats->max_execution_time_us = execution_time_us;
    }
    
    if (stats->min_execution_time_us == 0 || 
        execution_time_us < stats->min_execution_time_us) {
        stats->min_execution_time_us = execution_time_us;
    }
}
#endif

/**
 * @brief 安全执行回调函数
 * @param timer 定时器指针
 * @return 执行结果
 */
static TimerError_t ExecuteCallbackSafely(OptimizedTimer_t *timer) {
    if (timer->callback == NULL) {
        return TIMER_ERROR_INVALID_PARAM;
    }
    
    TIMER_PERF_START();
    
    // 设置异常处理
    if (setjmp(g_callback_exception_handler) == 0) {
        // 正常执行回调
        timer->callback(timer->context);
        
        TIMER_PERF_END("callback_execution");
        
#if TIMER_STATISTICS_ENABLED
        uint32_t execution_time = GetMicrosecondTick() - _perf_start;
        UpdateTimerStatistics(timer - g_timer_pool.timers, execution_time);
        
        // 检查执行时间是否超限
        if (g_system_config.enable_callback_timeout && 
            execution_time > g_system_config.max_callback_time_us) {
            return TIMER_ERROR_CALLBACK_TIMEOUT;
        }
#endif
        
        return TIMER_ERROR_NONE;
    } else {
        // 捕获到异常
        return TIMER_ERROR_CALLBACK_EXCEPTION;
    }
}

/**
 * @brief 处理单个定时器
 * @param timer_id 定时器ID
 * @return 是否处理了定时器
 */
static bool ProcessSingleTimer(uint8_t timer_id) {
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    uint32_t current_tick = Core_TimeBase_GetSystemTickCounter();
    
    // 检查定时器是否到期
    if (!IsTimeExpired(timer->next_expire_tick, current_tick)) {
        return false;
    }
    
    // 标记为已到期
    SetBit(&g_timer_pool.expired_bitmap, timer_id);
    
    // 处理不同类型的定时器
    switch (timer->timer_type) {
        case TIMER_TYPE_ONESHOT:
            // 一次性定时器：停止运行
            timer->state = TIMER_STATE_EXPIRED;
            ClearBit(&g_timer_pool.active_bitmap, timer_id);
            g_timer_pool.active_count--;
            break;
            
        case TIMER_TYPE_PERIODIC:
            // 周期性定时器：重新计算下次到期时间
            timer->next_expire_tick = current_tick + timer->duration_ticks;
            break;
            
        case TIMER_TYPE_DEBOUNCE:
            // 防抖定时器：检查是否有新的触发
            // 这里需要额外的逻辑来处理防抖
            break;
            
        default:
            break;
    }
    
    // 执行回调函数
    TimerError_t error = ExecuteCallbackSafely(timer);
    if (error != TIMER_ERROR_NONE && g_system_config.error_handler) {
        g_system_config.error_handler(timer_id, error);
    }
    
    return true;
}

/**
 * @brief 批量处理到期的定时器
 */
static void ProcessExpiredTimers(void) {
    uint32_t active_timers = g_timer_pool.active_bitmap;
    uint32_t processed_count = 0;
    
    TIMER_PERF_START();
    
    // 使用位操作快速遍历活跃定时器
    while (active_timers && processed_count < MAX_TIMERS) {
        uint8_t timer_id = FindFirstSetBit(active_timers);
        active_timers &= (active_timers - 1); // 清除最低位的1
        
        if (ProcessSingleTimer(timer_id)) {
            processed_count++;
        }
    }
    
    TIMER_PERF_END("batch_processing");
    
#if TIMER_PERFORMANCE_MONITORING
    g_performance_stats.timers_processed += processed_count;
    g_performance_stats.total_process_calls++;
#endif
}

// ============================================================================
// 公共API实现
// ============================================================================

/**
 * @brief 初始化定时器系统
 */
TimerError_t TimerSystem_Init(const TimerSystemConfig_t *config) {
    if (config == NULL) {
        return TIMER_ERROR_INVALID_PARAM;
    }
    
    // 清零所有数据结构
    memset(&g_timer_pool, 0, sizeof(g_timer_pool));
    memset(g_timer_groups, 0, sizeof(g_timer_groups));
    
    // 复制配置
    g_system_config = *config;
    
    // 初始化定时器组
    for (uint8_t i = 0; i < MAX_TIMER_GROUPS; i++) {
        g_timer_groups[i].group_id = i;
        g_timer_groups[i].enabled = true;
    }
    
#if TIMER_PERFORMANCE_MONITORING
    memset(&g_performance_stats, 0, sizeof(g_performance_stats));
#endif
    
    return TIMER_ERROR_NONE;
}

/**
 * @brief 创建新的定时器
 */
uint8_t TimerSystem_Create(const char *name, TimerType_t type, TimerPriority_t priority) {
    uint8_t timer_id = FindNextFreeIndex();
    
    if (timer_id >= MAX_TIMERS) {
        return MAX_TIMERS; // 表示创建失败
    }
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    
    // 初始化定时器
    memset(timer, 0, sizeof(OptimizedTimer_t));
    timer->timer_type = type;
    timer->priority = priority;
    timer->state = TIMER_STATE_STOPPED;
    timer->group_id = 0; // 默认组
    
#if TIMER_DEBUG_ENABLED
    timer->name = name;
#endif
    
#if TIMER_STATISTICS_ENABLED
    TimerStatistics_t *stats = &g_timer_pool.statistics[timer_id];
    memset(stats, 0, sizeof(TimerStatistics_t));
    stats->creation_time = Core_TimeBase_GetSystemTickCounter();
#endif
    
    return timer_id;
}

/**
 * @brief 启动定时器
 */
TimerError_t TimerSystem_Start(uint8_t timer_id, TimerCallback_t callback, void *context,
                              uint16_t seconds, uint16_t milliseconds) {
    TIMER_CHECK_ID(timer_id);
    TIMER_CHECK_POINTER(callback);
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    uint32_t current_tick = Core_TimeBase_GetSystemTickCounter();
    
    // 设置定时器参数
    timer->callback = callback;
    timer->context = context;
    timer->duration_seconds = seconds;
    timer->duration_milliseconds = milliseconds;
    timer->duration_ticks = TimeToTicks(seconds, milliseconds);
    timer->next_expire_tick = current_tick + timer->duration_ticks;
    timer->state = TIMER_STATE_RUNNING;
    
    // 添加到活跃定时器位图
    SetBit(&g_timer_pool.active_bitmap, timer_id);
    g_timer_pool.active_count++;
    
    return TIMER_ERROR_NONE;
}

/**
 * @brief 停止定时器
 */
TimerError_t TimerSystem_Stop(uint8_t timer_id) {
    TIMER_CHECK_ID(timer_id);
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    
    if (timer->state == TIMER_STATE_RUNNING) {
        timer->state = TIMER_STATE_STOPPED;
        ClearBit(&g_timer_pool.active_bitmap, timer_id);
        ClearBit(&g_timer_pool.expired_bitmap, timer_id);
        g_timer_pool.active_count--;
    }
    
    return TIMER_ERROR_NONE;
}

/**
 * @brief 检查定时器是否正在运行
 */
bool TimerSystem_IsRunning(uint8_t timer_id) {
    if (timer_id >= MAX_TIMERS) {
        return false;
    }
    
    return TestBit(g_timer_pool.active_bitmap, timer_id);
}

/**
 * @brief 获取定时器剩余时间
 */
uint32_t TimerSystem_GetRemainingTime(uint8_t timer_id) {
    if (timer_id >= MAX_TIMERS || !TimerSystem_IsRunning(timer_id)) {
        return 0;
    }
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    uint32_t current_tick = Core_TimeBase_GetSystemTickCounter();
    
    if (IsTimeExpired(timer->next_expire_tick, current_tick)) {
        return 0;
    }
    
    return timer->next_expire_tick - current_tick;
}

/**
 * @brief 主处理函数
 */
void TimerSystem_Process(void) {
    if (g_timer_pool.active_count == 0) {
        return; // 没有活跃定时器，直接返回
    }
    
    ProcessExpiredTimers();
}

/**
 * @brief 防抖定时器特殊处理
 */
TimerError_t TimerSystem_SetDebounce(uint8_t timer_id, uint16_t debounce_ms) {
    TIMER_CHECK_ID(timer_id);
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    uint32_t current_tick = Core_TimeBase_GetSystemTickCounter();
    
    if (timer->timer_type != TIMER_TYPE_DEBOUNCE) {
        return TIMER_ERROR_INVALID_PARAM;
    }
    
    // 重新设置到期时间（防抖逻辑）
    timer->duration_ticks = MillisecondsToTicks(debounce_ms);
    timer->next_expire_tick = current_tick + timer->duration_ticks;
    
    if (!TimerSystem_IsRunning(timer_id)) {
        // 如果定时器未运行，启动它
        SetBit(&g_timer_pool.active_bitmap, timer_id);
        timer->state = TIMER_STATE_RUNNING;
        g_timer_pool.active_count++;
    }
    
    return TIMER_ERROR_NONE;
}

// ============================================================================
// 调试和诊断函数
// ============================================================================

#if TIMER_DEBUG_ENABLED
/**
 * @brief 打印定时器系统状态
 */
void TimerSystem_PrintStatus(void) {
    printf("=== Timer System Status ===\n");
    printf("Active Timers: %d/%d\n", g_timer_pool.active_count, MAX_TIMERS);
    printf("Active Bitmap: 0x%08X\n", g_timer_pool.active_bitmap);
    printf("Expired Bitmap: 0x%08X\n", g_timer_pool.expired_bitmap);
    
    for (uint8_t i = 0; i < MAX_TIMERS; i++) {
        if (TestBit(g_timer_pool.active_bitmap, i)) {
            TimerSystem_PrintTimer(i);
        }
    }
    
#if TIMER_PERFORMANCE_MONITORING
    printf("\n=== Performance Stats ===\n");
    printf("Total Process Calls: %u\n", g_performance_stats.total_process_calls);
    printf("Timers Processed: %u\n", g_performance_stats.timers_processed);
    printf("Max Process Time: %u us\n", g_performance_stats.max_process_time_us);
    printf("Avg Process Time: %u us\n", g_performance_stats.avg_process_time_us);
#endif
}

/**
 * @brief 打印单个定时器信息
 */
void TimerSystem_PrintTimer(uint8_t timer_id) {
    if (timer_id >= MAX_TIMERS) {
        return;
    }
    
    OptimizedTimer_t *timer = &g_timer_pool.timers[timer_id];
    
    printf("Timer[%d]: %s\n", timer_id, timer->name ? timer->name : "unnamed");
    printf("  State: %d, Type: %d, Priority: %d\n", 
           timer->state, timer->timer_type, timer->priority);
    printf("  Duration: %u ticks, Next Expire: %u\n", 
           timer->duration_ticks, timer->next_expire_tick);
    printf("  Remaining: %u ticks\n", TimerSystem_GetRemainingTime(timer_id));
    
#if TIMER_STATISTICS_ENABLED
    TimerStatistics_t *stats = &g_timer_pool.statistics[timer_id];
    printf("  Expires: %u, Avg Exec: %u us, Max Exec: %u us\n",
           stats->total_expires,
           stats->total_expires > 0 ? stats->total_execution_time_us / stats->total_expires : 0,
           stats->max_execution_time_us);
#endif
}

/**
 * @brief 获取错误字符串
 */
const char* TimerSystem_GetErrorString(TimerError_t error) {
    static const char* error_strings[] = {
        "TIMER_ERROR_NONE",
        "TIMER_ERROR_INVALID_PARAM",
        "TIMER_ERROR_POOL_FULL",
        "TIMER_ERROR_NOT_FOUND",
        "TIMER_ERROR_CALLBACK_TIMEOUT",
        "TIMER_ERROR_CALLBACK_EXCEPTION",
        "TIMER_ERROR_OVERFLOW"
    };
    
    if (error < TIMER_ERROR_MAX) {
        return error_strings[error];
    }
    
    return "UNKNOWN_ERROR";
}
#endif // TIMER_DEBUG_ENABLED
