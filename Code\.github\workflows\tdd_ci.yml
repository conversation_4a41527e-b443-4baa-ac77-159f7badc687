name: TDD Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        build_type: [Debug, Release]
        compiler: [gcc, clang]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          cmake \
          libcmocka-dev \
          lcov \
          cppcheck \
          valgrind \
          clang \
          gcc
    
    - name: Set up compiler
      run: |
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          echo "CC=clang" >> $GITHUB_ENV
          echo "CXX=clang++" >> $GITHUB_ENV
        else
          echo "CC=gcc" >> $GITHUB_ENV
          echo "CXX=g++" >> $GITHUB_ENV
        fi
    
    - name: Configure CMake
      run: |
        cmake -B build \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DCMAKE_C_COMPILER=$CC
    
    - name: Build
      run: cmake --build build --parallel $(nproc)
    
    - name: Run Unit Tests
      run: |
        cd build
        make run_unit_tests
    
    - name: Run Integration Tests
      run: |
        cd build
        make run_integration_tests
    
    - name: Run All Tests
      run: |
        cd build
        ctest --output-on-failure --verbose
    
    - name: Generate Coverage Report (Debug only)
      if: matrix.build_type == 'Debug' && matrix.compiler == 'gcc'
      run: |
        cd build
        make coverage
    
    - name: Upload Coverage to Codecov
      if: matrix.build_type == 'Debug' && matrix.compiler == 'gcc'
      uses: codecov/codecov-action@v3
      with:
        file: build/coverage_filtered.info
        flags: unittests
        name: codecov-umbrella
    
    - name: Run Static Analysis
      run: |
        cppcheck --enable=all --xml --xml-version=2 \
          --suppress=missingIncludeSystem \
          --suppress=unusedFunction \
          Source/ 2> build/cppcheck.xml
    
    - name: Run Memory Check (Debug only)
      if: matrix.build_type == 'Debug'
      run: |
        cd build
        # 运行内存检查（仅对部分测试，避免CI超时）
        valgrind --tool=memcheck --leak-check=full --error-exitcode=1 \
          ./test_system_timer
    
    - name: Archive Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.build_type }}-${{ matrix.compiler }}
        path: |
          build/test_results/
          build/coverage/
          build/cppcheck.xml
    
    - name: Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2
      if: always()
      with:
        files: build/test_results/*.xml

  performance_test:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake libcmocka-dev
    
    - name: Configure and Build (Release)
      run: |
        cmake -B build -DCMAKE_BUILD_TYPE=Release
        cmake --build build --parallel $(nproc)
    
    - name: Run Performance Tests
      run: |
        cd build
        ./test_performance
    
    - name: Archive Performance Results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: build/performance_results/

  documentation:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Doxygen
      run: sudo apt-get install -y doxygen graphviz
    
    - name: Generate Documentation
      run: |
        doxygen Doxyfile
    
    - name: Deploy Documentation
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/html
