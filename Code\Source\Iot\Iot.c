/*******************************************************************************************************************//**
 * Brief description, until the first dot (use JAVADOC_AUTOBRIEF).
 *
 * \author $Author$
 * \remarks $Id$
 * \copyright
 * \file $Id$
 * @copyright Diehl Controls
 * @file IOT.c
 *
 **********************************************************************************************************************/


#include "Iot.h"
#include "miio_api.h"
#include "Iot_SpecHandler.h"
#include "Driver_AdSample.h"
#include "Parameter_TemperatureZone.h"
#include "DisplayInterface.h"
#include "Driver_DoorSwitch.h"
#include "Defrosting.h"
#include "Driver_Fan.h"
#include "Driver_Flash.h"
#include "Driver_CompFrequency.h"
#include "Driver_SingleDamper.h"
#include "Core_Types.h"
#include "FaultCode.h"
#include "Sbus_IceMaker.h"
#include "ParameterManager.h"
#include "SystemManager.h"
#include "CloudControl.h"
#include "FridgeRunner.h"
#include "Sbus_IceMaker.h"
#include "Sbus_Display.h"
#include "Sbus_Nfc.h"
#include "InverterUsart.h"
#include "Drive_Valve.h"
#include "VerticalBeamHeater.h"
#include "IO_Device.h"

static void IOT_FridgeParam(IotPropName_e IotIndex, TLong lvalue);
static void IOT_FaultReportedJudge(void);
static void IOT_FaultReportedJudge24h(void);
static void IOT_ModeReportedJudge(void);
static IotExecuteRet_e IOT_ModeSetJudge(TLong lvalue);
static void IOT_DoorAlarmReportedJudge(void);
static void IOT_RefTempReportedJudge(void);
static void IOT_RefSetReportedJudge(void);
static IotExecuteRet_e IOT_RefSetSetJudge(TLong lvalue);
static void IOT_RefOffReportedJudge(void);
static IotExecuteRet_e IOT_RefOffSetJudge(TLong lvalue);
static void IOT_RefIonSterReportedJudge(void);
static IotExecuteRet_e IOT_RefIonSterSetJudge(TLong lvalue);
static void IOT_FrzTempReportedJudge(void);
static void IOT_FrzSetReportedJudge(void);
static IotExecuteRet_e IOT_FrzSetSetJudge(TLong lvalue);
static void IOT_SuperFrzSetReportedJudge(void);
static void IOT_DeepSuperFrzSetReportedJudge(void);
static void IOT_FrzIonSterReportedJudge(void);
static IotExecuteRet_e IOT_FrzIonSterSetJudge(TLong lvalue);
static void IOT_RefVarTempReportedJudge(void);
static void IOT_RefVarSetReportedJudge(void);
static IotExecuteRet_e IOT_RefVarSetSetJudge(TLong lvalue);  
static void IOT_RefVarModeReportedJudge(void);
static IotExecuteRet_e IOT_RefVarModeSetJudge(TLong lvalue);
static void IOT_RefVarBottomTempReportedJudge(void);
static void IOT_RefVarTopTempReportedJudge(void);
static void IOT_RefVarBottomBackTempReportedJudge(void);
static void IOT_IceMakerModeReportedJudge(void);
static IotExecuteRet_e IOT_IceMakerModeSetJudge(TLong lvalue);
static void IOT_IceMakerStateReportedJudge(void);
static IotExecuteRet_e IOT_IceMakerStateSetJudge(TLong lvalue);
static void IOT_IceMakerReserveTimeReportedJudge(void);
static IotExecuteRet_e IOT_IceMakerReserveTimeSetJudge(TLong lvalue);
static void IOT_IceMakerSizeReportedJudge(void);
static IotExecuteRet_e IOT_IceMakerSizeSetJudge(TLong lvalue);
static void IOT_IceMakerReserveReportedJudge(void);
static IotExecuteRet_e IOT_IceMakerReserveSetJudge(TLong lvalue);
static void IOT_DefrostStatusReportedJudge(void);
static void IOT_DefrostTempReportedJudge(void);
static void IOT_RefDefrostTempReportedJudge(void);
static void IOT_RefDamperReportedJudge(void);
static void IOT_FrzFanLevelReportedJudge(void);
static void IOT_CompSpeedReportedJudge(void);
static void IOT_CoolFanLevelReportedJudge(void);
static void IOT_VarDamperReportedJudge(void);
static void IOT_RefVarDamperReportedJudge(void);
static void IOT_RoomTempReportedJudge(void);
static void IOT_HumidityReportedJudge(void);
static void IOT_DoorStateReportedJudge(void);
static IotExecuteRet_e IOT_WriteFactorySn(TLong lvalue);
static void IOT_ReadFactorySn(void);
static void IOT_ReadFactoryData(void);
static void IOT_PeekValleyReportedJudge(void);
static IotExecuteRet_e IOT_PeekValleySetJudge(TLong lvalue);
static void IOT_StrongCoolReportedJudge(void);
static IotExecuteRet_e IOT_StrongCoolSetJudge(TLong lvalue);
static void IOT_StrongMuteReportedJudge(void);
static IotExecuteRet_e IOT_StrongMuteSetJudge(TLong lvalue);
static void IOT_LingYunMuteReportedJudge(void);
static IotExecuteRet_e IOT_LingYunMuteSetJudge(TLong lvalue);
static void IOT_MuteModeReportedJudge(void);
static IotExecuteRet_e IOT_MuteModeSetJudge(TLong lvalue);
static void IOT_LingYunReportedJudge(void);
static IotExecuteRet_e IOT_LingYunSetJudge(TLong lvalue);
static void IOT_PeekValleyRefSetReportedJudge(void);
static IotExecuteRet_e IOT_PeekValleyRefSetJudge(TLong lvalue);
static void IOT_PeekValleyFrzSetReportedJudge(void);
static IotExecuteRet_e IOT_PeekValleyFrzSetJudge(TLong lvalue);
static void IOT_PeekValleyDeforstReportedJudge(void);
static IotExecuteRet_e IOT_PeekValleyDeforstSetJudge(TLong lvalue);
static void IOT_FoodAlarmReportedJudge(void);
static void IOT_FoodUnAlarmReportedJudge(void);
static void IOT_FoodAlarmRoomReportedJudge(void);
static IotExecuteRet_e IOT_FoodAlarmRoomSetJudge(TLong lvalue);
static void IOT_FoodAlarmDaysReportedJudge(void);
static IotExecuteRet_e IOT_FoodAlarmDaysSetJudge(TLong lvalue);
static void IOT_InverterFaultReportedJudge(void);          //10.12
static void IOT_SystemStatusReportedJudge(void);           //10.13
static void IOT_DefrostReasonReportedJudge(void);          //10.14
static void IOT_DefrostStepReportedJudge(void);            //10.15
static void IOT_CompressorStatusReportedJudge(void);       //10.16
static void IOT_FreezerStatusReportedJudge(void);          //10.17
static void IOT_RoomFreezerStateReportedJudge(void);       //10.18
static void IOT_RefFanLevelReportedJudge(void);            //10.19    
static void IOT_CompFeedbackFreqReportedJudge(void);       //10.20
static void IOT_CompFeedbackPowerReportedJudge(void);      //10.21
static void IOT_CompFeedbackVolReportedJudge(void);        //10.22
static void IOT_TotalRuntimeReportedJudge(void);           //10.23
static void IOT_CompTotalTimeReportedJudge(void);          //10.24
static void IOT_ComRuntimeReportedJudge(void);             //10.25
static void IOT_DefrostPreTimeReportedJudge(void);         //10.26
static void IOT_FrzHeaterOnTimeReportedJudge(void);        //10.27
static void IOT_SupermodeTimeReportedJudge(void);          //10.28
static void IOT_DefrostRuntimeReportedJudge(void);         //10.29
static void IOT_HeaterStatusReportedJudge(void);           //10.30
static void IOT_IceLoadStatusReportedJudge(void);          //10.31
static void IOT_IceMakingStatusReportedJudge(void);        //10.32
static void IOT_IceTargetTimeReportedJudge(void);          //10.33
static void IOT_IceRunTimeReportedJudge(void);             //10.34
static void IOT_IceCurrentTimeReportedJudge(void);         //10.35
static void IOT_ErrorQueryReportedJudge(void);             //10.36
static void IOT_ValveStatusReportedJudge(void);           //10.37
static void IOT_RefDoorTimesReportedJudge(void);         //10.38
static void IOT_RefDoorTimerReportedJudge(void);         //10.39
static void IOT_FrzDoorTimesReportedJudge(void);         //10.40
static void IOT_FrzDoorTimerReportedJudge(void);         //10.41

typedef void (*PFNFRIDGEPARAMREPORTHANDLER)(void);
typedef IotExecuteRet_e (*PFNFRIDGEPARAMSETHANDLER)(TLong lvalue);

typedef struct SFridgeParamIottoDev
{
   IotPropName_e  ePropName;                             // 当前属性
   PFNFRIDGEPARAMREPORTHANDLER pfnFridgeParamDevtoIot;   // 设备上报
   PFNFRIDGEPARAMSETHANDLER pfnFridgeParamIottoDev;      // IoT下发
} TSControlFridgeParam;

static const TSControlFridgeParam gdsFridgeParamCtlFunc[] =
{
    {IOT_PROP_FAULT, IOT_FaultReportedJudge, NULL},
    {IOT_PROP_MODE, IOT_ModeReportedJudge, IOT_ModeSetJudge},
    {IOT_PROP_DOOR_ALARM, IOT_DoorAlarmReportedJudge, NULL},
    {IOT_PROP_REF_SET, IOT_RefSetReportedJudge, IOT_RefSetSetJudge},
    {IOT_PROP_REF_OFF, IOT_RefOffReportedJudge, IOT_RefOffSetJudge},    
    {IOT_PROP_REF_IONSTER, IOT_RefIonSterReportedJudge, IOT_RefIonSterSetJudge},
    {IOT_PROP_FRZ_SET, IOT_FrzSetReportedJudge, IOT_FrzSetSetJudge},
    {IOT_PROP_SUPER_FRZ_SET, IOT_SuperFrzSetReportedJudge, NULL},
    {IOT_PROP_DEEPSUPER_FRZ_SET, IOT_DeepSuperFrzSetReportedJudge, NULL},    
    {IOT_PROP_FRZ_ICONSTER, IOT_FrzIonSterReportedJudge, IOT_FrzIonSterSetJudge},
    {IOT_PROP_REFVAR_SET, IOT_RefVarSetReportedJudge, IOT_RefVarSetSetJudge},    
    {IOT_PROP_REFVAR_MODE, IOT_RefVarModeReportedJudge, IOT_RefVarModeSetJudge},
    {IOT_PROP_ICEMAKER_MODE, IOT_IceMakerModeReportedJudge, IOT_IceMakerModeSetJudge},
    
    {IOT_PROP_ICEMAKER_STATE, IOT_IceMakerStateReportedJudge, IOT_IceMakerStateSetJudge},
    {IOT_PROP_ICEMAKER_RESERVETIME, IOT_IceMakerReserveTimeReportedJudge, IOT_IceMakerReserveTimeSetJudge}, 
    {IOT_PROP_ICEMAKER_SIZE, IOT_IceMakerSizeReportedJudge, IOT_IceMakerSizeSetJudge}, 
    {IOT_PROP_ICEMAKER_RESERVE, IOT_IceMakerReserveReportedJudge, IOT_IceMakerReserveSetJudge}, 
    {IOT_DL_PROP_DEFROST_STATUS, IOT_DefrostStatusReportedJudge, NULL},
    {IOT_DL_PROP_REF_DAMPER, IOT_RefDamperReportedJudge, NULL},
    {IOT_DL_PROP_FRZ_FAN, IOT_FrzFanLevelReportedJudge, NULL},
    {IOT_DL_PROP_COMP_SPEED, IOT_CompSpeedReportedJudge, NULL},
    {IOT_DL_PROP_COOL_FAN, IOT_CoolFanLevelReportedJudge, NULL},
    {IOT_DL_PROP_VAR_DAMPER, IOT_VarDamperReportedJudge, NULL},
    
    {IOT_DL_PROP_REFVAR_DAMPER, IOT_RefVarDamperReportedJudge, NULL},
    {IOT_PROP_DOOR_STATE, IOT_DoorStateReportedJudge, NULL},
    {IOT_PROP_PEEK_VALLEY_POWER, IOT_PeekValleyReportedJudge, IOT_PeekValleySetJudge},
    {IOT_PROP_LINGYUN_POWER, IOT_LingYunReportedJudge, IOT_LingYunSetJudge},
    {IOT_PROP_STRONG_COOL, IOT_StrongCoolReportedJudge, IOT_StrongCoolSetJudge},
    {IOT_PROP_STRONG_MUTE, IOT_StrongMuteReportedJudge, IOT_StrongMuteSetJudge},
    {IOT_PROP_LINGYUN_MUTE, IOT_LingYunMuteReportedJudge, IOT_LingYunMuteSetJudge},
    {IOT_PROP_MUTE_MODE, IOT_MuteModeReportedJudge, IOT_MuteModeSetJudge},
    {IOT_PROP_PEEK_VALLEY_REF_SET, IOT_PeekValleyRefSetReportedJudge, IOT_PeekValleyRefSetJudge},
    {IOT_PROP_PEEK_VALLEY_FRZ_SET, IOT_PeekValleyFrzSetReportedJudge, IOT_PeekValleyFrzSetJudge},
    {IOT_PROP_PEEK_VALLEY_DEFORST,IOT_PeekValleyDeforstReportedJudge, IOT_PeekValleyDeforstSetJudge},
    {IOT_PROP_FOOD_ALARM, IOT_FoodAlarmReportedJudge, NULL},
    {IOT_PROP_FOOD_UNALARM, IOT_FoodUnAlarmReportedJudge, NULL},
    {IOT_PROP_FOOD_ALARM_ROOM, IOT_FoodAlarmRoomReportedJudge, IOT_FoodAlarmRoomSetJudge},
    
    {IOT_PROP_FOOD_ALARM_DAYS, IOT_FoodAlarmDaysReportedJudge, IOT_FoodAlarmDaysSetJudge},
    {IOT_PROP_FACTORY_SN, IOT_ReadFactorySn, IOT_WriteFactorySn},
    {IOT_PROP_INVERTERFAULT, IOT_InverterFaultReportedJudge, NULL},          //10.12
    {IOT_PROP_SYSTEMSTATUS, IOT_SystemStatusReportedJudge, NULL},           //10.13
    {IOT_PROP_DEFROSTREASON, IOT_DefrostReasonReportedJudge, NULL},          //10.14
    {IOT_PROP_DEFROSTSTEP, IOT_DefrostStepReportedJudge, NULL},            //10.15
    {IOT_PROP_COMPRESSORSTATUS, IOT_CompressorStatusReportedJudge, NULL},       //10.16
    {IOT_PROP_FREEZERSTATUS, IOT_FreezerStatusReportedJudge, NULL},          //10.17
    {IOT_PROP_ROOMFREEZERSTATE, IOT_RoomFreezerStateReportedJudge, NULL},       //10.18
    {IOT_PROP_REFFANLEVEL, IOT_RefFanLevelReportedJudge, NULL},            //10.19    
	

    {IOT_PROP_DEFROSTPRETIME, IOT_DefrostPreTimeReportedJudge, NULL},         //10.26
    {IOT_PROP_HEATERSTATUS, IOT_HeaterStatusReportedJudge, NULL},           //10.30
    {IOT_PROP_ICELOADSTATUS, IOT_IceLoadStatusReportedJudge, NULL},          //10.31
    {IOT_PROP_ICEMAKINGSTATUS, IOT_IceMakingStatusReportedJudge, NULL},        //10.32
    {IOT_PROP_ICETARGETTIME, IOT_IceTargetTimeReportedJudge, NULL},          //10.33
    {IOT_PROP_ERRORQUERY, IOT_ErrorQueryReportedJudge, NULL},             //10.36
    {IOT_PROP_VALVE_STATUS, IOT_ValveStatusReportedJudge, NULL},           //10.37
    {IOT_PROP_REF_DOOR_TIMES, IOT_RefDoorTimesReportedJudge, NULL},         //10.38
    {IOT_PROP_REF_DOOR_TIMER, IOT_RefDoorTimerReportedJudge, NULL},         //10.39
    {IOT_PROP_FRZ_DOOR_TIMES, IOT_FrzDoorTimesReportedJudge, NULL},         //10.40
    {IOT_PROP_FRZ_DOOR_TIMER, IOT_FrzDoorTimerReportedJudge, NULL},         //10.41
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_1min[] =
{
    {IOT_PROP_FRZHEATERONTIME, IOT_FrzHeaterOnTimeReportedJudge, NULL},        //10.27
    {IOT_PROP_DEFROSTRUNTIME, IOT_DefrostRuntimeReportedJudge, NULL},         //10.29
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_20min[] =
{
    {IOT_PROP_REF_TEMP, IOT_RefTempReportedJudge, NULL},
    {IOT_PROP_FRZ_TEMP, IOT_FrzTempReportedJudge, NULL},
    {IOT_PROP_REFVAR_TEMP, IOT_RefVarTempReportedJudge, NULL},     
    {IOT_PROP_REFVAR_BOTTOMTEMP, IOT_RefVarBottomTempReportedJudge, NULL},
    {IOT_PROP_REFVAR_TOPTEMP, IOT_RefVarTopTempReportedJudge, NULL},
    {IOT_PROP_REFVAR_BOTTOMBACKTEMP, IOT_RefVarBottomBackTempReportedJudge, NULL},
    {IOT_DL_PROP_DEFROST_TEMP, IOT_DefrostTempReportedJudge, NULL}, 
    {IOT_PROP_ICERUNTIME, IOT_IceRunTimeReportedJudge, NULL},              //10.34
    {IOT_PROP_ICECURRENTTIME, IOT_IceCurrentTimeReportedJudge, NULL},      //10.35
    {IOT_PROP_REF_DEF_SNR, IOT_RefDefrostTempReportedJudge, NULL},         //10.43
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_30min[] =
{
    {IOT_PROP_COMPFEEDBACKFREQ, IOT_CompFeedbackFreqReportedJudge, NULL},       //10.20
    {IOT_PROP_COMPFEEDBACKPOWER, IOT_CompFeedbackPowerReportedJudge, NULL},      //10.21
    {IOT_PROP_COMPFEEDBACKVOL, IOT_CompFeedbackVolReportedJudge, NULL},        //10.22
    {IOT_PROP_TOTALRUNTIME, IOT_TotalRuntimeReportedJudge, NULL},           //10.23
    {IOT_PROP_COMPTOTALTIME, IOT_CompTotalTimeReportedJudge, NULL},          //10.24
    {IOT_PROP_COMRUNTIME, IOT_ComRuntimeReportedJudge, NULL},             //10.25
    {IOT_PROP_SUPERMODETIME, IOT_SupermodeTimeReportedJudge, NULL},          //10.28
};
static const TSControlFridgeParam gdsFridgeParamCtlFunc_60min[] =
{
    {IOT_DL_PROP_ROOMTTEMP, IOT_RoomTempReportedJudge, NULL},
    {IOT_DL_PROP_HUMIDITY, IOT_HumidityReportedJudge, NULL},
};

static const TSControlFridgeParam gdsFridgeParamCtlFunc_24h[] =
{
    {IOT_PROP_FAULT, IOT_FaultReportedJudge24h, NULL},
};

static void IOT_FridgeParam(IotPropName_e IotIndex, TLong lvalue)
{
   IotExecuteRet_e eRet = IOT_EXECUTE_OK;
   IotGeneralPropVal_t sGeneral;
   sGeneral.propType = IOT_TYPE_LONG;
#if 0
   // 获取当前IOt内存数据比较
   get_fridge_param_from_dev(IotIndex, &sGeneral);
   if (eRet != IOT_EXECUTE_OK)
   {
      mNOP();
   }
   if (lvalue == sGeneral.lValue)
   {
      return;
   }
#endif
   // 上传最新参数
   sGeneral.lValue = lvalue;
   eRet = set_fridge_param_by_dev(IotIndex, &sGeneral);
   if (eRet != IOT_EXECUTE_OK)
   {
     // mNOP();
   }
}

static void CalcTempIntegerValue(int16_t *pvalue)
{
    *pvalue = (*pvalue - 500) /10;
}

// 故障
static void IOT_FaultReportedJudge(void)// 上报
{   
	uint32_t error = 0;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);
    
    error = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
    IOT_FridgeParam(IOT_PROP_FAULT, (TLong)error);
}

// 故障
static void IOT_FaultReportedJudge24h(void)// 上报
{   
	uint32_t error = 0;

    IOT_FridgeParam(IOT_PROP_FAULT, (TLong)error);
}

// 模式
static void IOT_ModeReportedJudge(void)// 上报
{
    uint8_t ModeSet = Get_UserMode();
    IOT_FridgeParam(IOT_PROP_MODE, (TLong)ModeSet);
}

static IotExecuteRet_e IOT_ModeSetJudge(TLong lvalue)// 设置
{
    UserMode_t ModeSet = (uint8)lvalue;
    Set_UserMode(ModeSet);
    if (ModeSet == eTurboCool_Mode)
    {
        Set_RefDisable(0);
    }

    return IOT_EXECUTE_OK;
}

// 门报警状态
static void IOT_DoorAlarmReportedJudge(void)// 上报
{
    uint8_t  DoorStateAlarm = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);
    
//    if ((DoorStateRL & 0x08) || (DoorStateRR & 0x08) || (DoorStateFL & 0x08) || (DoorStateFR & 0x08))
//    {
//        DoorStateAlarm = 1;
//    }
//    else
//    {
//        DoorStateAlarm = 0;
//    }
    if (DoorStateRL & 0x08)
    {
        DoorStateAlarm |= 0x02;
    }
    if (DoorStateRR & 0x08)
    {
        DoorStateAlarm |= 0x04;
    }
    if (DoorStateFL & 0x08)
    {
        DoorStateAlarm |= 0x08;
    }
    if (DoorStateFR & 0x08)
    {
        DoorStateAlarm |= 0x10;
    }
    
   IOT_FridgeParam(IOT_PROP_DOOR_ALARM, (TLong)DoorStateAlarm);
}

// 冷藏温度
static void IOT_RefTempReportedJudge(void)// 上报
{
    int16_t u16_RefTemp = Get_SensorValue((SensorType_t)SENSOR_REF);
    
    CalcTempIntegerValue(&u16_RefTemp);
    IOT_FridgeParam(IOT_PROP_REF_TEMP, (TLong)u16_RefTemp);
}

// 冷藏设定
static void IOT_RefSetReportedJudge(void)// 上报
{
    uint8_t u8_RefSet;

    if(Get_UserMode() == eTurboCool_Mode)
    {
        u8_RefSet = REF_LEVEL_2;
    }
    else
    {
        GetSysParam(SYSPARAM_REFTEMP, &u8_RefSet);
    }
    u8_RefSet = u8_RefSet - 1;
    IOT_FridgeParam(IOT_PROP_REF_SET, (TLong)u8_RefSet);
}

static IotExecuteRet_e IOT_RefSetSetJudge(TLong lvalue)// 设置
{
    uint8_t u8_RefSet = (uint8)lvalue + 1;

    if ((Get_UserMode() == eFuzzy_Mode) || (Get_UserMode() == eTurboCool_Mode))
    {
        Set_UserMode(eManual_Mode);
    }

    if(Get_RefDisable() == true)
    {
        Set_RefDisable(0);
    }

    Update_RefSetTemp(u8_RefSet);

    return IOT_EXECUTE_OK;
}

// 冷藏关闭
static void IOT_RefOffReportedJudge(void)// 上报
{
    uint8_t refoff;
    bool disable = Get_RefDisable();

    refoff = disable ? 0 : 1;
    IOT_FridgeParam(IOT_PROP_REF_OFF, (TLong)refoff);
}

static IotExecuteRet_e IOT_RefOffSetJudge(TLong lvalue)// 设置
{
    uint8_t u8_RefOff = (uint8)lvalue;

    Set_RefDisable(u8_RefOff ? 0 : 1);
    return IOT_EXECUTE_OK;
}

// 冷藏室离子净味模块开关
static void IOT_RefIonSterReportedJudge(void)// 上报
{
    uint8_t value = 0;
    value = Get_RefIonEnable();
    IOT_FridgeParam(IOT_PROP_REF_IONSTER, (TLong)value);
}

static IotExecuteRet_e IOT_RefIonSterSetJudge(TLong lvalue)// 设置
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    Set_RefIonEnable(enable);
    return IOT_EXECUTE_OK;
}

// 冷冻温度
static void IOT_FrzTempReportedJudge(void)// 上报
{
    int16_t u16_FrzTemp = Get_SensorValue((SensorType_t)SENSOR_FRZ);
    
    CalcTempIntegerValue(&u16_FrzTemp);    
    IOT_FridgeParam(IOT_PROP_FRZ_TEMP, (TLong)u16_FrzTemp);
}

// 冷冻设定
static void IOT_FrzSetReportedJudge(void)// 上报
{
    uint8_t val;
    TLong u8_FrzSet;

    if ((Get_UserMode() == eTurboFreeze_Mode) || (Get_UserMode() == eDeepFreeze_Mode))
    {
        val = FRZ_LEVEL_F24;
    }
    else
    {
        GetSysParam(SYSPARAM_FRZTEMP, &val);
    }
    u8_FrzSet = val;
    u8_FrzSet = u8_FrzSet - (TLong)30;
    IOT_FridgeParam(IOT_PROP_FRZ_SET, (TLong)u8_FrzSet);
}

static IotExecuteRet_e IOT_FrzSetSetJudge(TLong lvalue)// 设置
{
    int8_t u8_FrzSet = (int8_t)lvalue + (int8_t)30;

    if ((Get_UserMode() == eFuzzy_Mode) || (Get_UserMode() == eTurboFreeze_Mode) ||
        (Get_UserMode() == eDeepFreeze_Mode))
    {
        Set_UserMode(eManual_Mode);
    }
    Update_FrzSetTemp(u8_FrzSet);

    return IOT_EXECUTE_OK;
}

// 速冻档位
static void IOT_SuperFrzSetReportedJudge(void)// 上报
{
    TLong u8_FrzSet =(TLong)-24;

    IOT_FridgeParam(IOT_PROP_SUPER_FRZ_SET, (TLong)u8_FrzSet);
}

// 深冷档位
static void IOT_DeepSuperFrzSetReportedJudge(void)// 上报
{
    TLong u8_FrzSet =(TLong)-30;

    IOT_FridgeParam(IOT_PROP_DEEPSUPER_FRZ_SET, (TLong)u8_FrzSet);
}

// 冷冻室离子净味模块开关
static void IOT_FrzIonSterReportedJudge(void)// 上报
{
    uint8_t value = 0;
    value = Get_FrzIonEnable();
    IOT_FridgeParam(IOT_PROP_FRZ_ICONSTER, (TLong)value);
}

static IotExecuteRet_e IOT_FrzIonSterSetJudge(TLong lvalue)// 设置
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    Set_FrzIonEnable(enable);
    return IOT_EXECUTE_OK;
}

// 变温舱温度
static void IOT_RefVarTempReportedJudge(void)// 上报
{
//    int16_t u16_VarTemp = Get_SensorValue((SensorType_t)SENSOR_VV);

//    CalcTempIntegerValue(&u16_VarTemp);
//    IOT_FridgeParam(IOT_PROP_REFVAR_TEMP, (TLong)u16_VarTemp);
}
// 变温舱设定
static void IOT_RefVarSetReportedJudge(void)// 上报
{

}

static IotExecuteRet_e IOT_RefVarSetSetJudge(TLong lvalue)// 设置
{
    return IOT_EXECUTE_OK;
}

// 变温舱设定
static void IOT_RefVarModeReportedJudge(void) // 上报
{
    uint8_t u8_VarSet = Get_RefVarSetTemp();
    
    IOT_FridgeParam(IOT_PROP_REFVAR_MODE, (TLong)u8_VarSet);
}

static IotExecuteRet_e IOT_RefVarModeSetJudge(TLong lvalue)// 设置
{
    uint8_t u8_VarSet = (uint8)lvalue;
    Update_RefVarSetTemp(u8_VarSet);

    return IOT_EXECUTE_OK;
}

static void IOT_ReadFactorySn(void)
{
    uint8_t sn[PRODUCT_SN_SIZE + 1] = {0};
    IotGeneralPropVal_t sGeneral;

    if(ReadProductSn(sn, PRODUCT_SN_SIZE) == 0)
    {
       sGeneral.propType = IOT_TYPE_STRING;
       strcpy(sGeneral.sValue,"\"");
       strcat(sGeneral.sValue, (const char*)sn);
       strcat(sGeneral.sValue, "\"");
       set_fridge_param_by_dev(IOT_PROP_FACTORY_SN, &sGeneral);
    }
}

static void IOT_ReadFactoryData(void)
{
    uint8_t sn[PRODUCT_SN_SIZE + 1] = {0};
    IotGeneralPropVal_t sGeneral;

    if(ReadProductSn(sn, PRODUCT_SN_SIZE) == 0)
    {

       sGeneral.propType = IOT_TYPE_STRING;
       strcpy(sGeneral.sValue,"\"");
       strcat(sGeneral.sValue, (const char*)sn);
       strcat(sGeneral.sValue, "\"");
       set_fridge_param_by_dev(IOT_PROP_FACTORY_SN, &sGeneral);
    }
}

static IotExecuteRet_e IOT_WriteFactorySn(TLong lvalue)
{
    uint8_t *psn = (uint8_t *)lvalue;

    if(Get_FridgeState() != eFridge_Factory)
    {
        return IOT_EXECUTE_CANNOT_WRITE;
    }

    if(WriteProductSn(&psn[1], PRODUCT_SN_SIZE, true) < 0)
    {
        return IOT_EXECUTE_FAIL;
    }

    ParameterSnUpdate();
    return IOT_EXECUTE_OK;
}

// 变温舱底部温度
static void IOT_RefVarBottomTempReportedJudge(void)// 上报
{
    int16_t u16_VarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOM);

    CalcTempIntegerValue(&u16_VarTemp);
    IOT_FridgeParam(IOT_PROP_REFVAR_BOTTOMTEMP, (TLong)u16_VarTemp);
}

// 变温舱顶部温度
static void IOT_RefVarTopTempReportedJudge(void)// 上报
{
    int16_t u16_VarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_TOP);

    CalcTempIntegerValue(&u16_VarTemp);
    IOT_FridgeParam(IOT_PROP_REFVAR_TOPTEMP, (TLong)u16_VarTemp);
}

// 变温舱底后部温度
static void IOT_RefVarBottomBackTempReportedJudge(void)// 上报
{
    int16_t u16_VarTemp = Get_SensorValue((SensorType_t)SENSOR_ICEMAKER_BOTTOMX);

    CalcTempIntegerValue(&u16_VarTemp);
    IOT_FridgeParam(IOT_PROP_REFVAR_BOTTOMBACKTEMP, (TLong)u16_VarTemp);
}
// 制冰机模式
static void IOT_IceMakerModeReportedJudge(void)// 上报
{
    uint8_t value = 0;
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &value);

    IOT_FridgeParam(IOT_PROP_ICEMAKER_MODE, (TLong)value);
}

static IotExecuteRet_e IOT_IceMakerModeSetJudge(TLong lvalue)// 设置
{    
    uint8_t value = (uint8)lvalue;
    SetSysParam(SYSPARAM_ICEMAKER_FUNC, value);
    
    return IOT_EXECUTE_OK;
}
// 制冰机状态
static void IOT_IceMakerStateReportedJudge(void)// 上报
{
    uint8_t value = 0;
    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_STATE, &value);

    IOT_FridgeParam(IOT_PROP_ICEMAKER_STATE, (TLong)value);
}

static IotExecuteRet_e IOT_IceMakerStateSetJudge(TLong lvalue)// 设置
{
    uint8_t value = (uint8)lvalue;
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_STATE, &value);

    return IOT_EXECUTE_OK;
}
//预约制冰剩余时间
static void IOT_IceMakerReserveTimeReportedJudge(void)// 上报
{
    uint32_t timer;
    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_RESERVE, &timer);
    IOT_FridgeParam(IOT_PROP_ICEMAKER_RESERVETIME, (TLong)timer);
}

//预约制冰剩余时间
static IotExecuteRet_e IOT_IceMakerReserveTimeSetJudge(TLong lvalue)// 设置
{
    uint32_t timer = (TLong)lvalue;
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_ICEMAKER_RESERVE, &timer);
    return IOT_EXECUTE_OK;
}

//冰块大小选择
static void IOT_IceMakerSizeReportedJudge(void)// 上报
{
    uint8_t value = 0;

    GetSysParam(SYSPARAM_ICEMAKER_VOLUME, &value);
    IOT_FridgeParam(IOT_PROP_ICEMAKER_SIZE, (TLong)value);
}

//冰块大小选择
static IotExecuteRet_e IOT_IceMakerSizeSetJudge(TLong lvalue)// 设置
{
    uint8_t value = (uint8_t)lvalue;

    SetSysParam(SYSPARAM_ICEMAKER_VOLUME, value);
    return IOT_EXECUTE_OK;
}
//预约用冰功能开启和停止
static void IOT_IceMakerReserveReportedJudge(void)// 上报
{
    uint8_t value;
    GetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_RESERVE_STATE, &value);
    IOT_FridgeParam(IOT_PROP_ICEMAKER_RESERVE, (TLong)value);
    return ;
}

//预约用冰功能开启和停止
static IotExecuteRet_e IOT_IceMakerReserveSetJudge(TLong lvalue)// 设置
{
    uint8_t value = (uint8_t)lvalue;
    SetIceMakerPropertyValue(ICEMAKER_PROPERTY_TYPE_RESERVE_STATE, &value);
    return IOT_EXECUTE_OK;
}

static void IOT_DefrostStatusReportedJudge(void)
{
    uint8_t defrost_mode = Get_DefrostMode();
    if((DefrostMode_t)eDefrostMode_None == defrost_mode)
    {
        defrost_mode = 0;
    }
    else
    {
        defrost_mode = 1;
    }
    IOT_FridgeParam(IOT_DL_PROP_DEFROST_STATUS, (TLong)defrost_mode);
}


static void IOT_DefrostTempReportedJudge(void)
{
    uint8_t defrost_mode = Get_DefrostMode();
    
    if (((DefrostMode_t)eDefrostMode_None < defrost_mode) && ((DefrostMode_t)eDefrostMode_Completed > defrost_mode))
    {
        int16_t u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_DEFROST);
            
        CalcTempIntegerValue(&u16_sensor_value);
        IOT_FridgeParam(IOT_DL_PROP_DEFROST_TEMP, (TLong)u16_sensor_value);
    }
}
 
static void IOT_RefDefrostTempReportedJudge(void)
{
    uint8_t defrost_mode = Get_DefrostMode();
    uint8_t refdefrost_mode = Get_RefFrostReduceMode();
    
    if ((((DefrostMode_t)eDefrostMode_None < defrost_mode) && ((DefrostMode_t)eDefrostMode_Completed > defrost_mode))
        || refdefrost_mode)
    {
        int16_t u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_REF_DEFROST);
            
        CalcTempIntegerValue(&u16_sensor_value);
        IOT_FridgeParam(IOT_DL_PROP_DEFROST_TEMP, (TLong)u16_sensor_value);
    }
}

static void IOT_RefDamperReportedJudge(void)// 上报
{
    uint8_t u8_device_state = (uint8_t)Get_RefDamperState();

    IOT_FridgeParam(IOT_DL_PROP_REF_DAMPER, (TLong)u8_device_state);
}

// 	冷冻风扇档位
static void IOT_FrzFanLevelReportedJudge(void)// 上报
{
    uint8_t u8_device_state = Get_FanDuty(FRZ_FAN);
	
    if (u8_device_state >= 30)
    {
        u8_device_state = u8_device_state / 5 - 5;
    }
    IOT_FridgeParam(IOT_DL_PROP_FRZ_FAN, (TLong)u8_device_state);
}

// 压缩机转速
static void IOT_CompSpeedReportedJudge(void)// 上报
{
    uint16_t u16_device_state = Get_CompFreq();
    
    u16_device_state = u16_device_state * 30;
    IOT_FridgeParam(IOT_DL_PROP_COMP_SPEED, (TLong)u16_device_state);
}

// 冷凝风扇档位
static void IOT_CoolFanLevelReportedJudge(void)// 上报
{
    uint8_t u8_device_state = Get_FanDuty(COOL_FAN);
	
    if (u8_device_state >= 30)
    {
        u8_device_state = u8_device_state / 5 - 5;
    }    
    IOT_FridgeParam(IOT_DL_PROP_COOL_FAN, (TLong)u8_device_state);
}

// 	变温室风门
static void IOT_VarDamperReportedJudge(void)// 上报
{
    uint8_t u8_device_state = 0;
    
    IOT_FridgeParam(IOT_DL_PROP_VAR_DAMPER, (TLong)u8_device_state);
}

// 变温舱风门
static void IOT_RefVarDamperReportedJudge(void)// 上报
{
    uint8_t u8_device_state = (uint8_t)Get_VarDamperState();

    IOT_FridgeParam(IOT_DL_PROP_REFVAR_DAMPER, (TLong)u8_device_state);
}

// 环境温度
static void IOT_RoomTempReportedJudge(void)// 上报
{
    int16_t u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_ROOM);
            
    CalcTempIntegerValue(&u16_sensor_value);
    IOT_FridgeParam(IOT_DL_PROP_ROOMTTEMP, (TLong)u16_sensor_value);
}

// 环境湿度
static void IOT_HumidityReportedJudge(void)// 上报
{
    uint8_t u8_sensor_value = Get_HumidityRange();

    u8_sensor_value = u8_sensor_value * 5 + 10;
    IOT_FridgeParam(IOT_DL_PROP_HUMIDITY, (TLong)u8_sensor_value);
}            

// 门开关
static void IOT_DoorStateReportedJudge(void)// 上报
{
    uint8_t  DoorState = 0;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);
    
    if (DoorStateRL & 0x02)
    {
        DoorState |= 0x01;
    }
    if (DoorStateRR & 0x02)
    {
        DoorState |= 0x02;
    }
    if (DoorStateFL & 0x02)
    {
        DoorState |= 0x04;
    }
    if (DoorStateFR & 0x02)
    {
        DoorState |= 0x08;
    }

    IOT_FridgeParam(IOT_PROP_DOOR_STATE, (TLong)DoorState);
}

static void IOT_PeekValleyReportedJudge(void)
{
    uint8_t u8_peek_valley_power = 0;

    GetSysParam(SYSPARAM_PEEK_VALLEY_POWER, &u8_peek_valley_power);
    IOT_FridgeParam(IOT_PROP_PEEK_VALLEY_POWER, (TLong)u8_peek_valley_power);
    return;
}

static IotExecuteRet_e IOT_PeekValleySetJudge(TLong lvalue)
{
    uint8_t u8_peek_valley_power = lvalue != 0 ? 1 : 0;
    SetSysParam(SYSPARAM_PEEK_VALLEY_POWER, u8_peek_valley_power);
    if(u8_peek_valley_power == 0)
    {
        Clean_PeekValleyFrzSet();
        Clean_PeekValleyRefSet();
    }
    return IOT_EXECUTE_OK;
}

static void IOT_StrongCoolReportedJudge(void)
{
    uint8_t enable = 0;

    if(true == Get_Strong_Cool())
    {
        enable = 1;
    }
    IOT_FridgeParam(IOT_PROP_STRONG_COOL, (TLong)enable);
    return;
}

static IotExecuteRet_e IOT_StrongCoolSetJudge(TLong lvalue)
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    //Set_Strong_Cool(enable);
    return IOT_EXECUTE_OK;
}

static void IOT_StrongMuteReportedJudge(void)
{
    uint8_t enable = 0;

    if(true == Get_Strong_Mute())
    {
        enable = 1;
    }
    IOT_FridgeParam(IOT_PROP_STRONG_MUTE, (TLong)enable);
    return;
}

static IotExecuteRet_e IOT_StrongMuteSetJudge(TLong lvalue)
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    //Set_Strong_Mute(enable);
    return IOT_EXECUTE_OK;
}

static void IOT_LingYunMuteReportedJudge(void)
{
    uint8_t value = 0;

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &value);
    IOT_FridgeParam(IOT_PROP_MUTE_MODE, (TLong)value);
}

static IotExecuteRet_e IOT_LingYunMuteSetJudge(TLong lvalue)
{
    uint8_t value = (uint8)lvalue;

    //SetSysParam(SYSPARAM_LINGYUN_MUTE, value);
    if(value == 0)
    {
        Set_Mute_Mode(LINYUN_MUTE_LOCAL);
    }    
    return IOT_EXECUTE_OK;
}

static void IOT_MuteModeReportedJudge(void)
{
    uint8_t value = 0;

    value = Get_Mute_Mode();
    IOT_FridgeParam(IOT_PROP_MUTE_MODE, (TLong)value);
}

static IotExecuteRet_e IOT_MuteModeSetJudge(TLong lvalue)
{
    uint8_t enable;
    uint8_t value = (uint8)lvalue;

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &enable);
    if(enable > 0)
    {
        Set_Mute_Mode(lvalue);
        return IOT_EXECUTE_OK;
    }
    return IOT_EXECUTE_FAIL;
}

static void IOT_LingYunReportedJudge(void)
{
    uint8_t enable = 0;

    GetSysParam(SYSPARAM_LINGYUN_POWER, &enable);
    IOT_FridgeParam(IOT_PROP_LINGYUN_POWER, (TLong)enable);
    return;
}

static IotExecuteRet_e IOT_LingYunSetJudge(TLong lvalue)
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    SetSysParam(SYSPARAM_LINGYUN_POWER, enable);
    return IOT_EXECUTE_OK;
}

static void IOT_PeekValleyRefSetReportedJudge(void)
{
    return;
}

static IotExecuteRet_e IOT_PeekValleyRefSetJudge(TLong lvalue)
{
    uint8_t u8_RefSet = (uint8)lvalue + 1;
    Set_PeekValleyRefSet(u8_RefSet);
    return IOT_EXECUTE_OK;
}

static void IOT_PeekValleyFrzSetReportedJudge(void)
{
    int8_t u8_FrzSet = GetPeekValleyPowerFrzTemp();
    if(u8_FrzSet > 0)
    {
        u8_FrzSet = u8_FrzSet - 30;
        IOT_FridgeParam(IOT_PROP_PEEK_VALLEY_FRZ_SET, (TLong)u8_FrzSet);
    }
    return;
}

static IotExecuteRet_e IOT_PeekValleyFrzSetJudge(TLong lvalue)
{
    int8_t u8_FrzSet = (int8_t)lvalue + (int8_t)30;

    Set_PeekValleyFrzSet(u8_FrzSet);
    return IOT_EXECUTE_OK;
}

static void IOT_PeekValleyDeforstReportedJudge(void)
{
    uint8_t enable = 0;

    enable = Get_PeekValleyDeforstFlag();
    IOT_FridgeParam(IOT_PROP_PEEK_VALLEY_DEFORST, (TLong)enable);
    return;
}

static IotExecuteRet_e IOT_PeekValleyDeforstSetJudge(TLong lvalue)
{
    uint8_t enable = lvalue != 0 ? 1 : 0;
    if(enable)
    {
        Force_PeekValleyPowerDeforst();
    }
    return IOT_EXECUTE_OK;
}

static void IOT_FoodAlarmReportedJudge(void)
{
    uint8_t u8_alarm = 0;

    GetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_FOOD_ALARM, &u8_alarm);
    IOT_FridgeParam(IOT_PROP_FOOD_ALARM, (TLong)u8_alarm);
}

static void IOT_FoodUnAlarmReportedJudge(void)
{
    uint8_t u8_alarm = 0;

    GetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_FOOD_UNALARM, &u8_alarm);
    IOT_FridgeParam(IOT_PROP_FOOD_UNALARM, (TLong)u8_alarm);
}

static void IOT_FoodAlarmRoomReportedJudge(void)
{

}

static IotExecuteRet_e IOT_FoodAlarmRoomSetJudge(TLong lvalue)
{
    uint16_t room = (uint16_t)lvalue;
    SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_FOOD_ALARM_ROOM, &room);
    return IOT_EXECUTE_OK;
}

static void IOT_FoodAlarmDaysReportedJudge(void)
{

}

static IotExecuteRet_e IOT_FoodAlarmDaysSetJudge(TLong lvalue)
{
    int16_t days = (int16_t)lvalue;
    SetDisplayPropertyValue(DISPLAY_PROPERTY_TYPE_FOOD_ALARM_DAYS, &days);
    return IOT_EXECUTE_OK;
}

static void IOT_InverterFaultReportedJudge(void)          //10.12
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_CompErrorState();
    IOT_FridgeParam(IOT_PROP_INVERTERFAULT, (TLong)u8_device_state);
}

static void IOT_SystemStatusReportedJudge(void)           //10.13
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_FridgeState();
    IOT_FridgeParam(IOT_PROP_SYSTEMSTATUS, (TLong)u8_device_state);
}
static void IOT_DefrostReasonReportedJudge(void)          //10.14
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_EnterDefrostingState();
    IOT_FridgeParam(IOT_PROP_DEFROSTREASON, (TLong)u8_device_state);
}

static void IOT_DefrostStepReportedJudge(void)            //10.15
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_DefrostMode();

    IOT_FridgeParam(IOT_PROP_DEFROSTSTEP, (TLong)u8_device_state);
}

static void IOT_CompressorStatusReportedJudge(void)       //10.16
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_CoolingCompState();
    IOT_FridgeParam(IOT_PROP_COMPRESSORSTATUS, (TLong)u8_device_state);
}

static void IOT_FreezerStatusReportedJudge(void)          //10.17
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_CoolingCapacityState();
    IOT_FridgeParam(IOT_PROP_FREEZERSTATUS, (TLong)u8_device_state);
}

static void IOT_RoomFreezerStateReportedJudge(void)       //10.18
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_ZoneCoolingState();
    IOT_FridgeParam(IOT_PROP_ROOMFREEZERSTATE, (TLong)u8_device_state);
}

static void IOT_RefFanLevelReportedJudge(void)            //10.19    
{
    uint8_t u8_device_state = 0;
    u8_device_state = Get_FanDuty(REF_FAN);
    IOT_FridgeParam(IOT_PROP_REFFANLEVEL, (TLong)u8_device_state);
}

static void IOT_CompFeedbackFreqReportedJudge(void)       //10.20
{
    uint16_t u16_device_state = 0;
    u16_device_state = (uint16_t)Get_CompFeedbackFreq();
    IOT_FridgeParam(IOT_PROP_COMPFEEDBACKFREQ, (TLong)u16_device_state);
}

static void IOT_CompFeedbackPowerReportedJudge(void)      //10.21
{
    uint16_t u16_device_state = 0;
    u16_device_state = (uint16_t)(Get_CompPower() / 2);
    IOT_FridgeParam(IOT_PROP_COMPFEEDBACKPOWER, (TLong)u16_device_state);
}

static void IOT_CompFeedbackVolReportedJudge(void)        //10.22
{
    uint16_t u16_device_state = 0;
    u16_device_state = (uint16_t)(Get_CompBusVoltage() / 2);
    IOT_FridgeParam(IOT_PROP_COMPFEEDBACKVOL, (TLong)u16_device_state);
}

static void IOT_TotalRuntimeReportedJudge(void)           //10.23
{
    uint16_t u16_device_state = 0;

    u16_device_state = Get_FridgeTotalOnTimeMinute();
    IOT_FridgeParam(IOT_PROP_TOTALRUNTIME, (TLong)u16_device_state);
}

static void IOT_CompTotalTimeReportedJudge(void)          //10.24
{
    uint16_t u16_device_state = 0;

    u16_device_state = Get_CompTotalOnTimeMinute();
    IOT_FridgeParam(IOT_PROP_COMPTOTALTIME, (TLong)u16_device_state);
}

static void IOT_ComRuntimeReportedJudge(void)             //10.25
{
    uint16_t u16_device_state = 0;
    u16_device_state = Get_CompStillOnTimeMinute();
    IOT_FridgeParam(IOT_PROP_COMRUNTIME, (TLong)u16_device_state);
}

static void IOT_DefrostPreTimeReportedJudge(void)         //10.26
{
    uint16_t u16_device_state = 0;
    u16_device_state = Get_PreCoolingtimeMinute();
    IOT_FridgeParam(IOT_PROP_DEFROSTPRETIME, (TLong)u16_device_state);
}

static void IOT_FrzHeaterOnTimeReportedJudge(void)        //10.27
{
    uint16_t u16_device_state = 0;
    u16_device_state = Get_DefrostHeaterOnSecond();
    IOT_FridgeParam(IOT_PROP_FRZHEATERONTIME, (TLong)u16_device_state);
}

static void IOT_SupermodeTimeReportedJudge(void)          //10.28
{
    uint16_t u16_device_state = 0;

    if((UserMode_t)eTurboCool_Mode == Get_UserMode())
    {
        u16_device_state = Get_TurboCoolTimeMinute();
    }
    else
    {
        u16_device_state = Get_TurboFreezeTimeMinute();
    }
    IOT_FridgeParam(IOT_PROP_SUPERMODETIME, (TLong)u16_device_state);
}

static void IOT_DefrostRuntimeReportedJudge(void)         //10.29
{
    uint16_t u16_device_state = 0;
    u16_device_state = Get_DefrostingtimeSecond();

    IOT_FridgeParam(IOT_PROP_DEFROSTRUNTIME, (TLong)u16_device_state);
}

static void IOT_HeaterStatusReportedJudge(void)           //10.30
{
    uint8_t u8_device_state = 0;

    
    //  if(Get_VerticalBeamHeaterDeviceState() == true)
    //  {
    //      u8_device_state |= 1 << 0;
    //  }
    
    if(Get_DefrostHeaterState() == true)
     {
         u8_device_state |= 1 << 1;
     }
    IOT_FridgeParam(IOT_PROP_HEATERSTATUS, (TLong)u8_device_state);
}

static void IOT_IceLoadStatusReportedJudge(void)          //10.31
{
    uint8_t u8_device_state = 0;
    
    GetIceMakerPropertyValue( ICEMAKER_PROPERTY_TYPE_ICEMAKER_LOAD_STATE, &u8_device_state);
    u8_device_state = u8_device_state & 0x01;
    IOT_FridgeParam(IOT_PROP_ICELOADSTATUS, (TLong)u8_device_state);
}

static void IOT_IceMakingStatusReportedJudge(void)        //10.32
{
    uint8_t u8_device_state = 0;
    
    GetIceMakerPropertyValue( ICEMAKER_PROPERTY_TYPE_ICEMAKER_DOING, &u8_device_state);
    IOT_FridgeParam(IOT_PROP_ICEMAKINGSTATUS, (TLong)u8_device_state);
}

static void IOT_IceTargetTimeReportedJudge(void)          //10.33
{
    uint16_t u16_device_state = 0;

    GetIceMakerPropertyValue( ICEMAKER_PROPERTY_TYPE_ICEMAKER_TARGET, &u16_device_state);
    IOT_FridgeParam(IOT_PROP_ICETARGETTIME, (TLong)u16_device_state);
}

static void IOT_IceRunTimeReportedJudge(void)             //10.34
{
   uint16_t u16_device_state = 0;
   
   GetIceMakerPropertyValue( ICEMAKER_PROPERTY_TYPE_ICEMAKER_HISTORY, &u16_device_state);
    IOT_FridgeParam(IOT_PROP_ICERUNTIME, (TLong)u16_device_state);
}

static void IOT_IceCurrentTimeReportedJudge(void)         //10.35
{
    uint16_t u16_device_state = 0;

    GetIceMakerPropertyValue( ICEMAKER_PROPERTY_TYPE_ICEMAKER_CURRENT, &u16_device_state);
    IOT_FridgeParam(IOT_PROP_ICECURRENTTIME, (TLong)u16_device_state);
}

static void IOT_ErrorQueryReportedJudge(void)             //10.36
{
    uint8_t u8_device_state = 0;
    bool err_state = Get_SensorError((uint8_t)SENSOR_HUMIDITY);

    if(err_state)
    {
        u8_device_state |= 1 << 0;
    }
    IOT_FridgeParam(IOT_PROP_ERRORQUERY, (TLong)u8_device_state);
}

static void IOT_ValveStatusReportedJudge(void)           //10.37
{
    uint8_t u8_device_state = 0;

    u8_device_state = Get_ValveState();
    IOT_FridgeParam(IOT_PROP_VALVE_STATUS, (TLong)u8_device_state);
}

static void IOT_RefDoorTimesReportedJudge(void)         //10.38
{
    uint8_t u8_device_state = 0;
    uint8_t defrost_mode = Get_DefrostMode();
    static uint8_t defrost_modeBck = 0;
    if (defrost_modeBck != defrost_mode)
    {
        defrost_modeBck = defrost_mode;
        if ((DefrostMode_t)eDefrostMode_Defrosting == defrost_mode)
        {
            u8_device_state = Get_DoorOpenCloseCounter(DOOR_REF_LEFT) + Get_DoorOpenCloseCounter(DOOR_REF_RIGHT);
            IOT_FridgeParam(IOT_PROP_REF_DOOR_TIMES, (TLong)u8_device_state);
        }
    }
}

static void IOT_RefDoorTimerReportedJudge(void)         //10.39
{
    uint16_t u16_device_state = 0;
    uint8_t defrost_mode = Get_DefrostMode();
    static uint8_t defrost_modeBck = 0;
    if (defrost_modeBck != defrost_mode)
    {
        defrost_modeBck = defrost_mode;
        if ((DefrostMode_t)eDefrostMode_Defrosting == defrost_mode)
        {
            u16_device_state = Get_DoorOpenTimeSecond(DOOR_REF);
            IOT_FridgeParam(IOT_PROP_REF_DOOR_TIMER, (TLong)u16_device_state);
        }
    }
}

static void IOT_FrzDoorTimesReportedJudge(void)         //10.40
{
    uint8_t u8_device_state = 0;
    uint8_t defrost_mode = Get_DefrostMode();
    static uint8_t defrost_modeBck = 0;
    if (defrost_modeBck != defrost_mode)
    {
        defrost_modeBck = defrost_mode;
        if ((DefrostMode_t)eDefrostMode_Defrosting == defrost_mode)
        {            
            u8_device_state = Get_DoorOpenTimeSecond(DOOR_FRZ);
            IOT_FridgeParam(IOT_PROP_FRZ_DOOR_TIMES, (TLong)u8_device_state);
        }
    }
}

static void IOT_FrzDoorTimerReportedJudge(void)         //10.41
{
    uint16_t u16_device_state = 0;
    uint8_t defrost_mode = Get_DefrostMode();
    static uint8_t defrost_modeBck = 0;
    if (defrost_modeBck != defrost_mode)
    {
        defrost_modeBck = defrost_mode;
        if ((DefrostMode_t)eDefrostMode_Defrosting == defrost_mode)
        {            
            u16_device_state = Get_DoorOpenTimeSecond(DOOR_FRZ);
            IOT_FridgeParam(IOT_PROP_FRZ_DOOR_TIMER, (TLong)u16_device_state);
        }
    }
}


static IotExecuteRet_e IOT_FridgeStartShakeSetJudge(TLong* value, TByte length);
static IotExecuteRet_e IOT_FridgeStopShakeSetJudge(TLong* value, TByte length);
static IotExecuteRet_e IOT_FactoryReset(TLong* value, TByte length);

typedef IotExecuteRet_e (*PFNFRIDGEACTIONSETHANDLER)(TLong* value, TByte length);

typedef struct SFridgeActionIottoDev
{
   IotActionName_e  eActionName;                           // 当前方法
   PFNFRIDGEACTIONSETHANDLER pfnFridgeActionIottoDev;      // IoT下发
} TSControlFridgeAction;

static const TSControlFridgeAction gdsFridgeActionCtlFunc[IOT_ACTION_MAX] =
{
    {IOT_ACTION_RESET, IOT_FactoryReset},
   {IOT_ACTION_PLUGIN_CONNECT, IOT_FridgeStartShakeSetJudge},
   {IOT_ACTION_PLUGIN_DISCONNECT, IOT_FridgeStopShakeSetJudge},

};

static IotExecuteRet_e IOT_FridgeStartShakeSetJudge(TLong* value, TByte length)// IOT下发
{
   return IOT_EXECUTE_OK;
}

static IotExecuteRet_e IOT_FridgeStopShakeSetJudge(TLong* value, TByte length)// IOT下发
{
   return IOT_EXECUTE_OK;
}

static IotExecuteRet_e IOT_FactoryReset(TLong* value, TByte length)// IOT下发
{
    RecoveryUsrSysParam();
    return IOT_EXECUTE_OK;
}

IotExecuteRet_e IOT_FridgeParamCallback(IotPropName_e propName, TLong value)
{
   TByte rIndex;
   IotExecuteRet_e eRet;

   for(rIndex = 0; rIndex < IOT_PROP_MAX; rIndex++)
   {
      if (propName == gdsFridgeParamCtlFunc[rIndex].ePropName)
      {
        if (gdsFridgeParamCtlFunc[rIndex].pfnFridgeParamIottoDev != NULL)
        {
           eRet = gdsFridgeParamCtlFunc[rIndex].pfnFridgeParamIottoDev(value);
        }
         break;
      }
   }

   return eRet;
}

IotExecuteRet_e IOT_FridgeActionCallback(IotActionName_e action, TLong* value, TByte length)
{
   TByte rIndex;
   IotExecuteRet_e eRet;

   for(rIndex = 0; rIndex < IOT_ACTION_MAX; rIndex++)
   {
      if (action == gdsFridgeActionCtlFunc[rIndex].eActionName)
      {
         eRet = gdsFridgeActionCtlFunc[rIndex].pfnFridgeActionIottoDev(value, length);
         break;
      }
   }

   return eRet;
}

typedef enum
{
   IOT_FirstProcedure = 0,
   IOT_FirstProcedure2,
   IOT_FirstProcedure3,
   IOT_FirstProcedure4,
   IOT_FirstProcedure5,
   IOT_SecondProcedure,
   IOT_ThirdProcedure,
   IOT_FourthProcedure,
   IOT_FifthProcedure,
   IOT_SixthProcedure,
   IOT_SeventhProcedure,
}IotTDatetimes_e;

// typedef enum
// {
//    IOT_FristCnt  = 10,
//    IOT_SecondCnt = IOT_PROP_MAX,
//    IOT_ThirdCnt  = IOT_PROP_MAX,
//    IOT_FourthCnt = IOT_PROP_MAX,
// }IotTDateCnt_e;

#define IOT_FirstCnt     10
#define IOT_FirstCnt2    20
#define IOT_FirstCnt3    30
#define IOT_FirstCnt4    40
#define IOT_FirstCnt5    50
#define IOT_SecondCnt   NUM_ELEMENTS(gdsFridgeParamCtlFunc)
#define IOT_ThirdCnt    NUM_ELEMENTS(gdsFridgeParamCtlFunc_20min)
#define IOT_FourthCnt   NUM_ELEMENTS(gdsFridgeParamCtlFunc_60min)
#define IOT_FifthCnt   NUM_ELEMENTS(gdsFridgeParamCtlFunc_24h)
#define IOT_SixthCnt   NUM_ELEMENTS(gdsFridgeParamCtlFunc_1min)
#define IOT_SeventhCnt   NUM_ELEMENTS(gdsFridgeParamCtlFunc_30min)

static TByte rIOTDateTimes = IOT_FirstProcedure;

void IOT_FridgeDataUpdate(void)
{
    TByte rIotCnt;
    int DeviceReportTime = Miio_GetDeviceReportTimeMin();
    static int DeviceReportTime_1min = 0;
    static int DeviceReportTime_20min = -19;
    static int DeviceReportTime_30min = -29;
    static int DeviceReportTime_60min = -59;
    static int DeviceReportTime_24h = -1439;
    
    switch (rIOTDateTimes)
    {
        case IOT_FirstProcedure:
        case IOT_FirstProcedure2:
        case IOT_FirstProcedure3:
        case IOT_FirstProcedure4:
        case IOT_FirstProcedure5:
            for(rIotCnt = rIOTDateTimes * 10; rIotCnt < ((rIOTDateTimes+1) * 10); rIotCnt++)
            {
	            if (gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot != NULL)
	            {
	                gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot();
	            }
            }
            rIOTDateTimes++;
            break;

        case IOT_SecondProcedure:
            for(rIotCnt = rIOTDateTimes * 10; rIotCnt < IOT_SecondCnt; rIotCnt++)
            {
                if (gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                {
                    gdsFridgeParamCtlFunc[rIotCnt].pfnFridgeParamDevtoIot();
                }
            }
            rIOTDateTimes++;
            break;
        case IOT_ThirdProcedure:
            if (DeviceReportTime - DeviceReportTime_20min >= IOT_DEVICE_REPORT_TIME_20MIN)
            {
                DeviceReportTime_20min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_ThirdCnt; rIotCnt++)
                {
                    if (gdsFridgeParamCtlFunc_20min[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                    {
                        gdsFridgeParamCtlFunc_20min[rIotCnt].pfnFridgeParamDevtoIot();
                    }
                }
            }
            rIOTDateTimes++;
            break;
        case IOT_FourthProcedure:
            if (DeviceReportTime - DeviceReportTime_60min >= IOT_DEVICE_REPORT_TIME_60MIN)
            {
                DeviceReportTime_60min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_FourthCnt; rIotCnt++)
                {                
                    if (gdsFridgeParamCtlFunc_60min[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                    {
                        gdsFridgeParamCtlFunc_60min[rIotCnt].pfnFridgeParamDevtoIot();
                    }
                }
            }
            rIOTDateTimes++;
            break;
        case IOT_FifthProcedure:
            if (DeviceReportTime - DeviceReportTime_24h >= IOT_DEVICE_REPORT_TIME_24H)
            {
                DeviceReportTime_24h = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_FifthCnt; rIotCnt++)
                {                
                    if (gdsFridgeParamCtlFunc_24h[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                    {
                        gdsFridgeParamCtlFunc_24h[rIotCnt].pfnFridgeParamDevtoIot();
                    }
                }
            }
            rIOTDateTimes++;
            break;
        case IOT_SixthProcedure:
            if (DeviceReportTime - DeviceReportTime_1min >= IOT_DEVICE_REPORT_TIME_1MIN)
            {
                DeviceReportTime_1min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_SixthCnt; rIotCnt++)
                {                
                    if (gdsFridgeParamCtlFunc_1min[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                    {
                        gdsFridgeParamCtlFunc_1min[rIotCnt].pfnFridgeParamDevtoIot();
                    }
                }
            }
            rIOTDateTimes++;
            break;
        case IOT_SeventhProcedure:
            if (DeviceReportTime - DeviceReportTime_30min >= IOT_DEVICE_REPORT_TIME_30MIN)
            {
                DeviceReportTime_30min = DeviceReportTime;
                for(rIotCnt = 0; rIotCnt < IOT_SeventhCnt; rIotCnt++)
                {                
                    if (gdsFridgeParamCtlFunc_30min[rIotCnt].pfnFridgeParamDevtoIot != NULL)
                    {
                        gdsFridgeParamCtlFunc_30min[rIotCnt].pfnFridgeParamDevtoIot();
                    }
                }
            }
            rIOTDateTimes = IOT_FirstProcedure;
            break;
      default:
         break;
   }
}

void IOT_EventTrigger(void)
{
    trig_ice_clean_finish();
    trig_food_remind_notice();
}

void IOT_Func(void)
{
    miio_command_rx_tx();
    IOT_FridgeDataUpdate();
    sync_fridge_params_set_by_iot(IOT_FridgeParamCallback);
    sync_action_cached_by_iot(IOT_FridgeActionCallback);
    IOT_EventTrigger();
}
/* ========== local functions ======================================================================================= */

/*=====================================================================@@mb=*/

