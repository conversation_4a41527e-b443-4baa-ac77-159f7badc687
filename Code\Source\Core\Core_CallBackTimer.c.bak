/*!
 * @file
 * @brief Enables timers to be used with callback functions and provides a way for
 * timers to be used without having to poll them.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "Core_TimeBase.h"
#include "Core_CallBackTimer.h"
#include "CORE_Assert.h"

// 优化的定时器池结构
typedef struct
{
    st_CoreCallbackTimer timers[U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL]; // 定时器数组
    uint32_t active_bitmap; // 活跃定时器位图
    uint8_t active_count; // 活跃定时器数量
    uint8_t next_free_id; // 下一个空闲ID
} TimerPool_Optimized;

// 全局定时器池
static TimerPool_Optimized g_timer_pool = { 0 };

// 内联辅助函数
static inline uint8_t FindFreeTimerID(void)
{
    uint32_t free_bitmap = ~g_timer_pool.active_bitmap;
    if(free_bitmap == 0)
    {
        return INVALID_TIMER_ID;
    }
    return FIND_FIRST_SET(free_bitmap);
}

static inline bool IsTimerActive(uint8_t timer_id)
{
    return MYTEST_BIT(g_timer_pool.active_bitmap, timer_id);
}

static inline void ActivateTimer(uint8_t timer_id)
{
    MYSET_BIT(g_timer_pool.active_bitmap, timer_id);
    g_timer_pool.active_count++;
}

static inline void DeactivateTimer(uint8_t timer_id)
{
    MYCLEAR_BIT(g_timer_pool.active_bitmap, timer_id);
    g_timer_pool.active_count--;
}

static bool ProcessTimer(st_CoreCallbackTimer *pst_CallBackTimer);

#if(true == CALLBACK_TIMER_HIGH_PRIORITY_ENABLED)
static void ProcessHighPriorityTimers(void);
#endif

#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
static void ProcessNormalPriorityTimers(void);
#endif

void Core_CallbackTimer_Init(void);

void Core_CallbackTimer_Update(void);

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,

    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority);

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer);

bool Core_CallbackTimer_B_IsTimerRunning(
    st_CoreCallbackTimer *pst_CallBackTimer);

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds);

static bool ProcessTimer(st_CoreCallbackTimer *pst_CallBackTimer)
{
    bool b_ActiveTimerProcessed = false;
    fpTimerCallBackFunction fp_CallBackFunction;

    if((st_CoreCallbackTimer *)(NULL) != pst_CallBackTimer)
    {
        b_ActiveTimerProcessed = true;

        if(true == Core_TimerLib_IsTimerExpired(&(pst_CallBackTimer->st_Timer)))
        {
            fp_CallBackFunction = pst_CallBackTimer->fp_CallBackFunction;

            if((uint8_t)(eCallbackTimer_Type_Periodic) ==
                pst_CallBackTimer->u8_TimerType)
            {
                Core_TimerLib_TimerStart(&(pst_CallBackTimer->st_Timer),
                    pst_CallBackTimer->u16_DurationSeconds,
                    pst_CallBackTimer->u16_DurationMilliSeconds);
            }
            else
            {
                // 使用优化的停止函数
                Core_CallbackTimer_StopByID(pst_CallBackTimer->u8_TimerID);
            }

            fp_CallBackFunction();
        }
    }

    return (b_ActiveTimerProcessed);
}

#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
static void ProcessNormalPriorityTimers(void)
{
    // 快速检查：如果没有活跃定时器，直接返回
    if(g_timer_pool.active_count == 0)
    {
        return;
    }

    static uint8_t last_processed_id = 0; // 保持轮询公平性
    uint8_t processed_count = 0;
    uint8_t current_id = last_processed_id;

    do
    {
        // 检查当前ID是否活跃
        if(IsTimerActive(current_id))
        {
            st_CoreCallbackTimer *pst_CallBackTimer = &g_timer_pool.timers[current_id];

            if(ProcessTimer(pst_CallBackTimer))
            {
                last_processed_id = (current_id + 1) % U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL;
                return; // 处理了一个定时器就返回，保持原有行为
            }
        }

        // 移动到下一个定时器
        current_id = (current_id + 1) % U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL;
        processed_count++;

    } while(current_id != last_processed_id &&
        processed_count < U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL);

    // 更新下次开始位置
    last_processed_id = current_id;
}
#endif

void Core_CallbackTimer_Init(void)
{
    // 清零所有数据
    memset(&g_timer_pool, 0, sizeof(g_timer_pool));

    // 初始化所有定时器ID
    for(uint8_t i = 0; i < U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL; i++)
    {
        g_timer_pool.timers[i].u8_TimerID = i;
    }
}

void Core_CallbackTimer_Update(void)
{
#if(true == CALLBACK_TIMER_NORMAL_PRIORITY_ENABLED)
    ProcessNormalPriorityTimers();
#endif
}

void Core_CallbackTimer_TimerStart(
    st_CoreCallbackTimer *pst_CallBackTimer,
    fpTimerCallBackFunction fp_CallBackFunction,
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds,
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority)
{
    ENSURE(en_TimerType < eCallbackTimer_Type_Max);
    ENSURE(en_Priority < eCallbackTimer_Priority_Max);
    ENSURE(pst_CallBackTimer != (st_CoreCallbackTimer *)(NULL));

    uint8_t timer_id = pst_CallBackTimer->u8_TimerID;
    ENSURE(timer_id < U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL);

    pst_CallBackTimer->u16_DurationSeconds = u16_DurationSeconds;
    pst_CallBackTimer->u16_DurationMilliSeconds = u16_DurationMilliSeconds;
    pst_CallBackTimer->u8_TimerType = (uint8_t)en_TimerType;
    pst_CallBackTimer->u8_Priority = (uint8_t)en_Priority;
    pst_CallBackTimer->fp_CallBackFunction = fp_CallBackFunction;

    // 使用位操作替代List插入 - O(1)操作
    ActivateTimer(timer_id);

    Core_TimerLib_TimerStart(&(pst_CallBackTimer->st_Timer),
        pst_CallBackTimer->u16_DurationSeconds,
        pst_CallBackTimer->u16_DurationMilliSeconds);
}

void Core_CallbackTimer_TimerStop(
    st_CoreCallbackTimer *pst_CallBackTimer)
{
    if(pst_CallBackTimer != NULL &&
        pst_CallBackTimer->u8_TimerID < U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL)
    {
        uint8_t timer_id = pst_CallBackTimer->u8_TimerID;

        // 使用位操作替代List移除 - O(1)操作
        if(IsTimerActive(timer_id))
        {
            DeactivateTimer(timer_id);
            Core_TimerLib_TimerStop(&pst_CallBackTimer->st_Timer);
            pst_CallBackTimer->fp_CallBackFunction = NULL;
        }
    }
}

bool Core_CallbackTimer_B_IsTimerRunning(
    st_CoreCallbackTimer *pst_CallBackTimer)
{
    bool b_TimerIsRunning = false;

    if(((st_CoreCallbackTimer *)(NULL) != pst_CallBackTimer) &&
        (pst_CallBackTimer->u8_TimerID < U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL))
    {
        // 使用位操作替代List查找 - O(1)操作
        b_TimerIsRunning = IsTimerActive(pst_CallBackTimer->u8_TimerID);
    }

    return (b_TimerIsRunning);
}

void Core_CallbackTimer_TimeRemaining(
    st_CoreCallbackTimer *pst_CallBackTimer, uint16_t *pu16_Seconds, uint16_t *pu16_Milliseconds)
{
    if(((uint16_t *)NULL != pu16_Seconds) &&
        ((uint16_t *)NULL != pu16_Milliseconds))
    {
        if(true ==
            Core_CallbackTimer_B_IsTimerRunning(pst_CallBackTimer))
        {
            *pu16_Seconds = pst_CallBackTimer->st_Timer.st_Duration.u16_Seconds;
            *pu16_Milliseconds =
                pst_CallBackTimer->st_Timer.st_Duration.u16_Ticks /
                COREUSER_TIMEBASE_NUM_TICKS_PER_MILLISECOND;
        }
        else
        {
            *pu16_Seconds = 0;
            *pu16_Milliseconds = 0;
        }
    }
}

// ============================================================================
// 新增的优化API函数
// ============================================================================

uint8_t Core_CallbackTimer_Create(
    EN_Core_CallbackTimer_Type en_TimerType,
    EN_Core_CallbackTimer_Priority en_Priority)
{
    // 快速查找空闲ID - O(1)操作
    uint8_t timer_id = FindFreeTimerID();

    if(timer_id >= U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL)
    {
        return INVALID_TIMER_ID; // 池满
    }

    // 初始化定时器
    st_CoreCallbackTimer *timer = &g_timer_pool.timers[timer_id];
    memset(timer, 0, sizeof(st_CoreCallbackTimer));
    timer->u8_TimerType = (uint8_t)en_TimerType;
    timer->u8_Priority = (uint8_t)en_Priority;
    timer->u8_TimerID = timer_id;

    return timer_id;
}

bool Core_CallbackTimer_StartByID(
    uint8_t u8_TimerID,
    fpTimerCallBackFunction fp_CallBackFunction,
    uint16_t u16_DurationSeconds,
    uint16_t u16_DurationMilliSeconds)
{
    // 参数检查
    if(u8_TimerID >= U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL ||
        fp_CallBackFunction == NULL)
    {
        return false;
    }

    st_CoreCallbackTimer *timer = &g_timer_pool.timers[u8_TimerID];

    // 设置定时器参数
    timer->fp_CallBackFunction = fp_CallBackFunction;
    timer->u16_DurationSeconds = u16_DurationSeconds;
    timer->u16_DurationMilliSeconds = u16_DurationMilliSeconds;

    // 启动基础定时器
    Core_TimerLib_TimerStart(&timer->st_Timer, u16_DurationSeconds, u16_DurationMilliSeconds);

    // 激活定时器 - O(1)位操作
    ActivateTimer(u8_TimerID);

    return true;
}

bool Core_CallbackTimer_StopByID(uint8_t u8_TimerID)
{
    // 参数检查
    if(u8_TimerID >= U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL)
    {
        return false;
    }

    // 检查定时器是否活跃
    if(!IsTimerActive(u8_TimerID))
    {
        return false; // 定时器未运行
    }

    st_CoreCallbackTimer *timer = &g_timer_pool.timers[u8_TimerID];

    // 停止基础定时器
    Core_TimerLib_TimerStop(&timer->st_Timer);

    // 停用定时器 - O(1)位操作
    DeactivateTimer(u8_TimerID);

    // 清除回调函数
    timer->fp_CallBackFunction = NULL;

    return true;
}

bool Core_CallbackTimer_IsRunningByID(uint8_t u8_TimerID)
{
    if(u8_TimerID >= U8_CORE_CALLBACK_TIMER_POOL_SIZE_NORMAL)
    {
        return false;
    }

    // O(1)位操作
    return IsTimerActive(u8_TimerID);
}

uint8_t Core_CallbackTimer_GetActiveCount(void)
{
    return g_timer_pool.active_count;
}
